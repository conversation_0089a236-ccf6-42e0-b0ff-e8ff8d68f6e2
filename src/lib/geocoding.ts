export interface GeocodedAddress {
  address: string;
  latitude: number;
  longitude: number;
  confidence: number;
}

export interface MapboxFeature {
  id: string;
  type: 'Feature';
  place_type: string[];
  relevance: number;
  properties: Record<string, any>;
  text: string;
  place_name: string;
  center: [number, number]; // [longitude, latitude]
  geometry: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
}

export interface MapboxResponse {
  type: 'FeatureCollection';
  query: string[];
  features: MapboxFeature[];
  attribution: string;
}

// Import geocoding cache
import { GeocodingCache } from './geocoding-cache';

/**
 * Geocode a single address using Mapbox API (with caching)
 */
export async function geocodeAddress(address: string): Promise<GeocodedAddress | null> {
  const mapboxToken = process.env.MAPBOX_API_KEY || process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
  
  if (!mapboxToken) {
    throw new Error('Mapbox API key is not configured');
  }

  if (!address.trim()) {
    return null;
  }

  const trimmedAddress = address.trim();
  const geocodingCache = GeocodingCache.getInstance();

  // Check cache first
  const cachedResult = geocodingCache.get(trimmedAddress);
  if (cachedResult) {
    return {
      address: cachedResult.geocodedAddress,
      latitude: cachedResult.latitude,
      longitude: cachedResult.longitude,
      confidence: 1.0, // Cache hits assume high confidence
    };
  }

  try {
    const encodedAddress = encodeURIComponent(trimmedAddress);
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodedAddress}.json?access_token=${mapboxToken}&limit=1`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Mapbox API error: ${response.status} ${response.statusText}`);
    }
    
    const data: MapboxResponse = await response.json();
    
    if (data.features.length === 0) {
      return null;
    }
    
    const feature = data.features[0];
    if (!feature) {
      return null;
    }
    
    const [longitude, latitude] = feature.center;
    
    const result: GeocodedAddress = {
      address: feature.place_name,
      latitude,
      longitude,
      confidence: feature.relevance,
    };

    // Cache the successful result
    geocodingCache.set(trimmedAddress, {
      address: trimmedAddress,
      geocodedAddress: feature.place_name,
      latitude,
      longitude,
      source: 'mapbox',
    });
    
    return result;
  } catch (error) {
    console.error('Geocoding error:', error);
    throw new Error('Failed to geocode address');
  }
}

/**
 * Geocode multiple addresses in batch (with caching)
 */
export async function geocodeAddresses(addresses: string[]): Promise<(GeocodedAddress | null)[]> {
  const geocodingCache = GeocodingCache.getInstance();
  const results: (GeocodedAddress | null)[] = [];
  const addressesToGeocode: { address: string; index: number }[] = [];
  
  // First pass: check cache for all addresses
  for (let i = 0; i < addresses.length; i++) {
    const address = addresses[i];
    if (!address?.trim()) {
      results[i] = null;
      continue;
    }

    const trimmedAddress = address.trim();
    const cachedResult = geocodingCache.get(trimmedAddress);
    
    if (cachedResult) {
      // Use cached result
      results[i] = {
        address: cachedResult.geocodedAddress,
        latitude: cachedResult.latitude,
        longitude: cachedResult.longitude,
        confidence: 1.0, // Cache hits assume high confidence
      };
    } else {
      // Mark for API geocoding
      addressesToGeocode.push({ address: trimmedAddress, index: i });
      results[i] = null; // Placeholder
    }
  }

  // Second pass: geocode uncached addresses via API
  if (addressesToGeocode.length > 0) {
    console.log(`Geocoding ${addressesToGeocode.length} addresses via Mapbox API`);
    
    for (const { address, index } of addressesToGeocode) {
      try {
        const result = await geocodeAddress(address);
        results[index] = result;
        
        // Add a small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Failed to geocode address: ${address}`, error);
        results[index] = null;
      }
    }
  } else {
    console.log('All addresses found in geocoding cache, no API calls needed');
  }
  
  return results;
}

/**
 * Get user's current location using browser geolocation API
 */
export function getCurrentLocation(): Promise<{ latitude: number; longitude: number }> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        });
      },
      (error) => {
        reject(new Error(`Geolocation error: ${error.message}`));
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 600000, // 10 minutes
      }
    );
  });
}

/**
 * Reverse geocode coordinates to get an address (with caching)
 */
export async function reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
  const mapboxToken = process.env.MAPBOX_API_KEY || process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
  
  if (!mapboxToken) {
    throw new Error('Mapbox API key is not configured');
  }

  const geocodingCache = GeocodingCache.getInstance();
  
  // Create a cache key for reverse geocoding based on rounded coordinates
  // Round to ~100m precision to allow reasonable cache hits for nearby locations
  const roundedLat = Math.round(latitude * 1000) / 1000;
  const roundedLng = Math.round(longitude * 1000) / 1000;
  const cacheKey = `reverse:${roundedLat},${roundedLng}`;
  
  // Check cache first (using the cacheKey as the "address" parameter)
  const cachedResult = geocodingCache.get(cacheKey);
  if (cachedResult) {
    return cachedResult.geocodedAddress;
  }

  try {
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${mapboxToken}&limit=1`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Mapbox API error: ${response.status} ${response.statusText}`);
    }
    
    const data: MapboxResponse = await response.json();
    
    if (data.features.length === 0) {
      return null;
    }
    
    const feature = data.features[0];
    if (!feature) {
      return null;
    }

    const address = feature.place_name;

    // Cache the successful reverse geocoding result
    geocodingCache.set(cacheKey, {
      address: cacheKey, // Use cache key as "original address"
      geocodedAddress: address,
      latitude: roundedLat,
      longitude: roundedLng,
      source: 'mapbox',
    });
    
    return address;
  } catch (error) {
    console.error('Reverse geocoding error:', error);
    throw new Error('Failed to reverse geocode coordinates');
  }
} 