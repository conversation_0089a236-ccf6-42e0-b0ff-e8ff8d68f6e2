// Subscription limits and validation for OptiRoutePro

export enum PlanTier {
  TRIAL = 'TRIAL',
  PRO = 'PRO',
  UNLIMITED = 'UNLIMITED'
}

export interface PlanLimits {
  // Route optimization limits
  monthlyRouteLimit: number;
  maxStopsPerRoute: number;
  maxImagesPerUpload: number;
  
  // Feature access
  canUseImageUpload: boolean;
  canUseBulkOptimization: boolean;
  canExportRoutes: boolean;
  canUseAdvancedAnalytics: boolean;
  
  // API limits
  maxGeocodingRequestsPerDay: number;
  maxOptimizationRequestsPerHour: number;
  
  // Storage limits
  maxSavedRoutes: number;
  routeHistoryDays: number;
  
  // Trial information
  trialDays?: number;
}

export const PLAN_LIMITS: Record<PlanTier, PlanLimits> = {
  [PlanTier.TRIAL]: {
    monthlyRouteLimit: 50,
    maxStopsPerRoute: 55,
    maxImagesPerUpload: 500,
    canUseImageUpload: true,
    canUseBulkOptimization: true,
    canExportRoutes: true,
    canUseAdvancedAnalytics: true,
    maxGeocodingRequestsPerDay: 2000,
    maxOptimizationRequestsPerHour: 100,
    maxSavedRoutes: 200,
    routeHistoryDays: 365,
    trialDays: 7,
  },
  [PlanTier.PRO]: {
    monthlyRouteLimit: 50,
    maxStopsPerRoute: 55,
    maxImagesPerUpload: 500,
    canUseImageUpload: true,
    canUseBulkOptimization: true,
    canExportRoutes: true,
    canUseAdvancedAnalytics: true,
    maxGeocodingRequestsPerDay: 2000,
    maxOptimizationRequestsPerHour: 100,
    maxSavedRoutes: 200,
    routeHistoryDays: 365,
  },
  [PlanTier.UNLIMITED]: {
    monthlyRouteLimit: Infinity,
    maxStopsPerRoute: 100,
    maxImagesPerUpload: Infinity,
    canUseImageUpload: true,
    canUseBulkOptimization: true,
    canExportRoutes: true,
    canUseAdvancedAnalytics: true,
    maxGeocodingRequestsPerDay: Infinity,
    maxOptimizationRequestsPerHour: Infinity,
    maxSavedRoutes: Infinity,
    routeHistoryDays: Infinity,
  },
};

export interface UsageStats {
  routesOptimizedThisMonth: number;
  geocodingRequestsToday: number;
  optimizationRequestsThisHour: number;
  savedRoutesCount: number;
}

export interface LimitCheckResult {
  allowed: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  currentUsage?: number;
  limit?: number;
}

export class SubscriptionLimitsService {
  static getPlanLimits(planTier: PlanTier): PlanLimits {
    return PLAN_LIMITS[planTier];
  }

  static checkRouteOptimizationLimit(
    planTier: PlanTier,
    usage: UsageStats,
    stopsCount: number
  ): LimitCheckResult {
    const limits = this.getPlanLimits(planTier);

    // Check monthly route limit
    if (usage.routesOptimizedThisMonth >= limits.monthlyRouteLimit) {
      return {
        allowed: false,
        reason: `Monthly route limit reached (${limits.monthlyRouteLimit} routes)`,
        upgradeRequired: planTier === PlanTier.TRIAL,
        currentUsage: usage.routesOptimizedThisMonth,
        limit: limits.monthlyRouteLimit,
      };
    }

    // Check stops per route limit
    if (stopsCount > limits.maxStopsPerRoute) {
      return {
        allowed: false,
        reason: `Too many stops (${stopsCount}). Maximum allowed: ${limits.maxStopsPerRoute}`,
        upgradeRequired: planTier === PlanTier.TRIAL,
        currentUsage: stopsCount,
        limit: limits.maxStopsPerRoute,
      };
    }

    // Check hourly optimization limit
    if (usage.optimizationRequestsThisHour >= limits.maxOptimizationRequestsPerHour) {
      return {
        allowed: false,
        reason: `Hourly optimization limit reached (${limits.maxOptimizationRequestsPerHour} per hour)`,
        upgradeRequired: planTier === PlanTier.TRIAL,
        currentUsage: usage.optimizationRequestsThisHour,
        limit: limits.maxOptimizationRequestsPerHour,
      };
    }

    return { allowed: true };
  }

  static checkImageUploadLimit(
    planTier: PlanTier,
    imageCount: number
  ): LimitCheckResult {
    const limits = this.getPlanLimits(planTier);

    if (!limits.canUseImageUpload) {
      return {
        allowed: false,
        reason: 'Image upload not available in your plan',
        upgradeRequired: true,
      };
    }

    if (imageCount > limits.maxImagesPerUpload) {
      return {
        allowed: false,
        reason: `Too many images (${imageCount}). Maximum allowed: ${limits.maxImagesPerUpload}`,
        upgradeRequired: planTier === PlanTier.TRIAL,
        currentUsage: imageCount,
        limit: limits.maxImagesPerUpload,
      };
    }

    return { allowed: true };
  }

  static checkGeocodingLimit(
    planTier: PlanTier,
    usage: UsageStats
  ): LimitCheckResult {
    const limits = this.getPlanLimits(planTier);

    if (usage.geocodingRequestsToday >= limits.maxGeocodingRequestsPerDay) {
      return {
        allowed: false,
        reason: `Daily geocoding limit reached (${limits.maxGeocodingRequestsPerDay} requests)`,
        upgradeRequired: planTier === PlanTier.TRIAL,
        currentUsage: usage.geocodingRequestsToday,
        limit: limits.maxGeocodingRequestsPerDay,
      };
    }

    return { allowed: true };
  }

  static checkSavedRoutesLimit(
    planTier: PlanTier,
    usage: UsageStats
  ): LimitCheckResult {
    const limits = this.getPlanLimits(planTier);

    if (usage.savedRoutesCount >= limits.maxSavedRoutes) {
      return {
        allowed: false,
        reason: `Saved routes limit reached (${limits.maxSavedRoutes} routes)`,
        upgradeRequired: planTier === PlanTier.TRIAL,
        currentUsage: usage.savedRoutesCount,
        limit: limits.maxSavedRoutes,
      };
    }

    return { allowed: true };
  }

  static checkFeatureAccess(
    planTier: PlanTier,
    feature: keyof Pick<PlanLimits, 'canUseBulkOptimization' | 'canExportRoutes' | 'canUseAdvancedAnalytics'>
  ): LimitCheckResult {
    const limits = this.getPlanLimits(planTier);

    if (!limits[feature]) {
      return {
        allowed: false,
        reason: `${feature.replace('canUse', '').replace('can', '')} not available in your plan`,
        upgradeRequired: true,
      };
    }

    return { allowed: true };
  }

  static getUsagePercentage(current: number, limit: number): number {
    if (limit === Infinity) return 0;
    return Math.min(Math.round((current / limit) * 100), 100);
  }

  static getRemainingUsage(current: number, limit: number): number {
    if (limit === Infinity) return Infinity;
    return Math.max(limit - current, 0);
  }

  static isNearLimit(current: number, limit: number, threshold = 0.8): boolean {
    if (limit === Infinity) return false;
    return current / limit >= threshold;
  }

  static getUpgradeMessage(planTier: PlanTier, feature?: string): string {
    const baseMessage = feature 
      ? `${feature} requires a higher plan.`
      : 'You\'ve reached your plan limit.';

    switch (planTier) {
      case PlanTier.TRIAL:
        return `${baseMessage} Upgrade to Pro for more routes, stops, and advanced features.`;
      case PlanTier.PRO:
        return `${baseMessage} Upgrade to Unlimited for unlimited routes and premium features.`;
      default:
        return baseMessage;
    }
  }
} 