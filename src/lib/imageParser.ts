import Tesseract from 'tesseract.js';

export interface ParsedAddress {
  address: string;
  confidence: number;
  lineNumber: number;
}

export interface OCRResult {
  text: string;
  confidence: number;
  addresses: ParsedAddress[];
}

/**
 * Extract text from an image using Tesseract.js
 */
export async function extractTextFromImage(imageFile: File): Promise<OCRResult> {
  try {
    // Use the recognize method directly which is simpler in v6
    const { data } = await Tesseract.recognize(imageFile, 'eng', {
      logger: (m) => {
        if (m.status === 'recognizing text') {
          console.log(`OCR Progress: ${Math.round(m.progress * 100)}%`);
        }
      },
    });

    // Extract addresses from the recognized text
    const addresses = extractAddressesFromText(data.text);

    return {
      text: data.text,
      confidence: data.confidence,
      addresses,
    };
  } catch (error) {
    console.error('OCR error:', error);
    throw new Error('Failed to extract text from image');
  }
}

/**
 * Extract addresses from text using regex patterns
 */
export function extractAddressesFromText(text: string): ParsedAddress[] {
  const addresses: ParsedAddress[] = [];
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  // Common address patterns
  const addressPatterns = [
    // US addresses: number + street name + optional unit + city + state + zip
    /^\d+\s+[A-Za-z\s]+(?:St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive|Ln|Lane|Way|Ct|Court|Pl|Place|Pkwy|Parkway)(?:\s+#?\d+[A-Z]?)?,?\s*[A-Za-z\s]+,?\s*[A-Z]{2}\s*\d{5}(?:-\d{4})?$/i,
    
    // Street address with city and state (without zip)
    /^\d+\s+[A-Za-z\s]+(?:St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive|Ln|Lane|Way|Ct|Court|Pl|Place|Pkwy|Parkway)(?:\s+#?\d+[A-Z]?)?,?\s*[A-Za-z\s]+,?\s*[A-Z]{2}$/i,
    
    // International addresses: number + street name
    /^\d+\s+[A-Za-z\s]+(St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive|Ln|Lane|Way|Ct|Court|Pl|Place|Pkwy|Parkway)(?:\s+[A-Za-z0-9\s,.-]*)?$/i,
    
    // Simple street address patterns
    /^\d+\s+[A-Za-z\s]+(St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive|Ln|Lane|Way|Ct|Court|Pl|Place|Pkwy|Parkway)/i,
    
    // Business names with addresses
    /^[A-Za-z\s&'.-]+,?\s*\d+\s+[A-Za-z\s]+(St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive|Ln|Lane|Way|Ct|Court|Pl|Place|Pkwy|Parkway)/i,
    
    // PO Box addresses
    /^P\.?O\.?\s*Box\s+\d+,?\s*[A-Za-z\s]+,?\s*[A-Z]{2}\s*\d{5}(?:-\d{4})?$/i,
  ];

  // Additional patterns for common address components
  const cityStateZipPattern = /[A-Za-z\s]+,?\s*[A-Z]{2}\s*\d{5}(?:-\d{4})?$/;
  const streetNumberPattern = /^\d+/;

  lines.forEach((line, index) => {
    // Clean up the line
    const cleanLine = line
      .replace(/[^\w\s,.#-]/g, '') // Remove special characters except common address chars
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    if (cleanLine.length < 10) { // Skip very short lines
      return;
    }

    // Check against address patterns
    for (const pattern of addressPatterns) {
      if (pattern.test(cleanLine)) {
        addresses.push({
          address: cleanLine,
          confidence: 0.8, // High confidence for regex matches
          lineNumber: index + 1,
        });
        return; // Found a match, move to next line
      }
    }

    // Additional heuristic checks for potential addresses
    const hasStreetNumber = streetNumberPattern.test(cleanLine);
    const hasStreetType = /\b(St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive|Ln|Lane|Way|Ct|Court|Pl|Place|Pkwy|Parkway)\b/i.test(cleanLine);
    const hasCityStateZip = cityStateZipPattern.test(cleanLine);

    // If it has typical address components, include it with lower confidence
    if ((hasStreetNumber && hasStreetType) || hasCityStateZip) {
      addresses.push({
        address: cleanLine,
        confidence: 0.6, // Lower confidence for heuristic matches
        lineNumber: index + 1,
      });
    }
  });

  // Remove duplicates and sort by confidence
  const uniqueAddresses = addresses
    .filter((addr, index, arr) => 
      arr.findIndex(a => a.address.toLowerCase() === addr.address.toLowerCase()) === index
    )
    .sort((a, b) => b.confidence - a.confidence);

  return uniqueAddresses;
}

/**
 * Process multiple images and extract addresses from all of them
 */
export async function processMultipleImages(files: File[]): Promise<ParsedAddress[]> {
  const allAddresses: ParsedAddress[] = [];

  for (const file of files) {
    try {
      const result = await extractTextFromImage(file);
      allAddresses.push(...result.addresses);
    } catch (error) {
      console.error(`Failed to process image ${file.name}:`, error);
      // Continue processing other images even if one fails
    }
  }

  // Remove duplicates across all images
  const uniqueAddresses = allAddresses
    .filter((addr, index, arr) => 
      arr.findIndex(a => a.address.toLowerCase() === addr.address.toLowerCase()) === index
    )
    .sort((a, b) => b.confidence - a.confidence);

  return uniqueAddresses;
}

/**
 * Validate if a string looks like a valid address
 */
export function isValidAddress(address: string): boolean {
  if (!address || address.length < 10) {
    return false;
  }

  // Must contain at least some common address elements
  const hasNumber = /\d/.test(address);
  const hasStreetType = /\b(St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard|Dr|Drive|Ln|Lane|Way|Ct|Court|Pl|Place|Pkwy|Parkway)\b/i.test(address);
  const hasLetters = /[A-Za-z]/.test(address);

  return hasNumber && hasLetters && (hasStreetType || address.split(' ').length >= 3);
}

/**
 * Clean and normalize an address string
 */
export function normalizeAddress(address: string): string {
  return address
    .trim()
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/\b(St)\b/gi, 'Street')
    .replace(/\b(Ave)\b/gi, 'Avenue')
    .replace(/\b(Rd)\b/gi, 'Road')
    .replace(/\b(Blvd)\b/gi, 'Boulevard')
    .replace(/\b(Dr)\b/gi, 'Drive')
    .replace(/\b(Ln)\b/gi, 'Lane')
    .replace(/\b(Ct)\b/gi, 'Court')
    .replace(/\b(Pl)\b/gi, 'Place')
    .replace(/\b(Pkwy)\b/gi, 'Parkway');
} 