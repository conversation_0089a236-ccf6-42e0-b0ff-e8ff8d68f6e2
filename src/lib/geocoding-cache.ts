// Geocoding Cache Management
// Caches geocoded addresses to reduce API usage and improve performance

interface CachedGeocodingResult {
  address: string; // Original address as entered
  geocodedAddress: string; // Full geocoded address from API
  latitude: number;
  longitude: number;
  timestamp: number; // When this was cached
  source: 'mapbox' | 'browser'; // Which geocoding service was used
}

interface GeocodingCacheEntry {
  result: CachedGeocodingResult;
  expiresAt: number;
}

const CACHE_KEY = 'optiroute-geocoding-cache';
const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
const MAX_CACHE_ENTRIES = 1000; // Prevent unlimited cache growth

export class GeocodingCache {
  private static instance: GeocodingCache;
  private cache: Map<string, GeocodingCacheEntry> = new Map();

  private constructor() {
    this.loadFromStorage();
    this.setupCleanupInterval();
  }

  public static getInstance(): GeocodingCache {
    if (!GeocodingCache.instance) {
      GeocodingCache.instance = new GeocodingCache();
    }
    return GeocodingCache.instance;
  }

  /**
   * Normalize address string for consistent cache keys
   */
  private normalizeAddress(address: string): string {
    return address
      .toLowerCase()
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[.,#]/g, '') // Remove common punctuation
      .replace(/\b(street|st|avenue|ave|road|rd|boulevard|blvd|drive|dr|lane|ln|court|ct|place|pl)\b/g, (match) => {
        // Normalize common street abbreviations
        const abbrevMap: { [key: string]: string } = {
          'street': 'st', 'avenue': 'ave', 'road': 'rd', 'boulevard': 'blvd',
          'drive': 'dr', 'lane': 'ln', 'court': 'ct', 'place': 'pl'
        };
        return abbrevMap[match] || match;
      });
  }

  /**
   * Load cache from localStorage
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(CACHE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        const now = Date.now();
        
        // Convert stored object back to Map and filter expired entries
        Object.entries(data).forEach(([key, entry]) => {
          const cacheEntry = entry as GeocodingCacheEntry;
          if (cacheEntry.expiresAt > now) {
            this.cache.set(key, cacheEntry);
          }
        });
        
        console.log(`Loaded ${this.cache.size} geocoding cache entries`);
      }
    } catch (error) {
      console.error('Failed to load geocoding cache:', error);
      this.cache.clear();
    }
  }

  /**
   * Save cache to localStorage
   */
  private saveToStorage(): void {
    try {
      // Convert Map to object for JSON serialization
      const data = Object.fromEntries(this.cache.entries());
      localStorage.setItem(CACHE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save geocoding cache:', error);
      // If storage is full, try to clear some old entries
      this.cleanup();
    }
  }

  /**
   * Clean up expired entries and enforce size limits
   */
  private cleanup(): void {
    const now = Date.now();
    let removedCount = 0;

    // Remove expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt <= now) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    // If still too many entries, remove oldest ones
    if (this.cache.size > MAX_CACHE_ENTRIES) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].result.timestamp - b[1].result.timestamp);
      
      const toRemove = this.cache.size - MAX_CACHE_ENTRIES;
      for (let i = 0; i < toRemove; i++) {
        const entryToRemove = entries[i];
        if (entryToRemove) {
          this.cache.delete(entryToRemove[0]);
          removedCount++;
        }
      }
    }

    if (removedCount > 0) {
      console.log(`Cleaned up ${removedCount} old geocoding cache entries`);
      this.saveToStorage();
    }
  }

  /**
   * Setup periodic cleanup (every hour)
   */
  private setupCleanupInterval(): void {
    // Only run cleanup in browser environment
    if (typeof window !== 'undefined') {
      setInterval(() => {
        this.cleanup();
      }, 60 * 60 * 1000); // 1 hour
    }
  }

  /**
   * Get cached geocoding result for an address
   */
  public get(address: string): CachedGeocodingResult | null {
    const normalizedAddress = this.normalizeAddress(address);
    const entry = this.cache.get(normalizedAddress);

    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (entry.expiresAt <= Date.now()) {
      this.cache.delete(normalizedAddress);
      this.saveToStorage();
      return null;
    }

    console.log(`Cache hit for address: ${address}`);
    return entry.result;
  }

  /**
   * Store geocoding result in cache
   */
  public set(address: string, result: Omit<CachedGeocodingResult, 'timestamp'>): void {
    const normalizedAddress = this.normalizeAddress(address);
    const now = Date.now();

    const cacheEntry: GeocodingCacheEntry = {
      result: {
        ...result,
        timestamp: now,
      },
      expiresAt: now + CACHE_DURATION,
    };

    this.cache.set(normalizedAddress, cacheEntry);
    console.log(`Cached geocoding result for: ${address}`);

    // Save to localStorage
    this.saveToStorage();

    // Check if we need cleanup
    if (this.cache.size > MAX_CACHE_ENTRIES * 1.1) {
      this.cleanup();
    }
  }

  /**
   * Check if an address is cached (without returning the result)
   */
  public has(address: string): boolean {
    return this.get(address) !== null;
  }

  /**
   * Clear all cached entries
   */
  public clear(): void {
    this.cache.clear();
    localStorage.removeItem(CACHE_KEY);
    console.log('Geocoding cache cleared');
  }

  /**
   * Get cache statistics
   */
  public getStats(): {
    size: number;
    oldestEntry: string | null;
    newestEntry: string | null;
    totalSize: string;
  } {
    if (this.cache.size === 0) {
      return {
        size: 0,
        oldestEntry: null,
        newestEntry: null,
        totalSize: '0 KB',
      };
    }

    const entries = Array.from(this.cache.values());
    const timestamps = entries.map(e => e.result.timestamp);
    const oldest = Math.min(...timestamps);
    const newest = Math.max(...timestamps);

    // Estimate size
    const dataString = JSON.stringify(Object.fromEntries(this.cache.entries()));
    const sizeKB = Math.round((dataString.length * 2) / 1024); // Rough estimate

    return {
      size: this.cache.size,
      oldestEntry: new Date(oldest).toLocaleDateString(),
      newestEntry: new Date(newest).toLocaleDateString(),
      totalSize: `${sizeKB} KB`,
    };
  }

  /**
   * Export cache data for debugging
   */
  public exportCache(): CachedGeocodingResult[] {
    return Array.from(this.cache.values()).map(entry => entry.result);
  }
} 