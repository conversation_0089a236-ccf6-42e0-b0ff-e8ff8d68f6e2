import type { <PERSON> } from "@/config/links";

/**
 * Filters an array of potentially null/undefined links, returning only the valid Link objects.
 * @param links - The array of links to filter.
 * @returns An array containing only valid Link objects.
 */
export function filterEnabledLinks(
  links: (Link | null | undefined)[] | undefined,
): Link[] {
  if (!links) {
    return [];
  }
  // The `filter(<PERSON><PERSON>an)` removes null/undefined, and `as <PERSON>[]` asserts the correct type.
  return links.filter(Boolean) as Link[];
}
