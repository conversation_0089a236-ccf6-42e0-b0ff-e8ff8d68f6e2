export type NavigationApp = 'google' | 'apple';

export interface NavigationDestination {
  address?: string;
  latitude: number;
  longitude: number;
}

/**
 * Open navigation to a destination in the specified app
 */
export function openNavigation(
  destination: NavigationDestination,
  app: NavigationApp
): void {
  const { latitude, longitude, address } = destination;

  if (app === 'google') {
    openGoogleMaps(latitude, longitude, address);
  } else if (app === 'apple') {
    openAppleMaps(latitude, longitude, address);
  } else {
    throw new Error(`Unsupported navigation app: ${app}`);
  }
}

/**
 * Open Google Maps with navigation to the destination
 */
function openGoogleMaps(
  latitude: number,
  longitude: number,
  address?: string
): void {
  // Use address if available, otherwise use coordinates
  const destination = address 
    ? encodeURIComponent(address)
    : `${latitude},${longitude}`;

  // Different URL schemes based on platform
  if (isMobileDevice()) {
    // Use mobile app URL scheme if available, fallback to web
    const mobileUrl = `comgooglemaps://?daddr=${destination}&directionsmode=driving`;
    const webUrl = `https://maps.google.com/maps?daddr=${destination}&dir_action=navigate`;
    
    // Try to open mobile app first
    const link = document.createElement('a');
    link.href = mobileUrl;
    link.click();
    
    // Fallback to web version after a short delay
    setTimeout(() => {
      window.open(webUrl, '_blank');
    }, 500);
  } else {
    // Desktop: open web version
    const webUrl = `https://maps.google.com/maps?daddr=${destination}&dir_action=navigate`;
    window.open(webUrl, '_blank');
  }
}

/**
 * Open Apple Maps with navigation to the destination
 */
function openAppleMaps(
  latitude: number,
  longitude: number,
  address?: string
): void {
  // Use address if available, otherwise use coordinates
  const destination = address 
    ? encodeURIComponent(address)
    : `${latitude},${longitude}`;

  if (isMobileDevice() && isIOSDevice()) {
    // Use Apple Maps URL scheme on iOS
    const url = `maps://?daddr=${destination}&dirflg=d`;
    window.location.href = url;
  } else {
    // Fallback to web version (works on all platforms)
    const webUrl = `https://maps.apple.com/?daddr=${destination}&dirflg=d`;
    window.open(webUrl, '_blank');
  }
}

/**
 * Detect if the device is mobile
 */
function isMobileDevice(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
}

/**
 * Detect if the device is iOS
 */
function isIOSDevice(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * Get the best navigation app for the current platform
 */
export function getDefaultNavigationApp(): NavigationApp {
  if (isIOSDevice()) {
    return 'apple';
  }
  return 'google';
}

/**
 * Check if a navigation app is available on the current platform
 */
export function isNavigationAppAvailable(app: NavigationApp): boolean {
  if (app === 'google') {
    return true; // Google Maps is available on all platforms
  }
  
  if (app === 'apple') {
    // Apple Maps works best on iOS, but web version works everywhere
    return true;
  }
  
  return false;
}

/**
 * Get human-readable name for navigation app
 */
export function getNavigationAppName(app: NavigationApp): string {
  switch (app) {
    case 'google':
      return 'Google Maps';
    case 'apple':
      return 'Apple Maps';
    default:
      return 'Unknown';
  }
}

/**
 * Open turn-by-turn navigation for multiple waypoints
 */
export function openMultiWaypointNavigation(
  waypoints: NavigationDestination[],
  app: NavigationApp
): void {
  if (waypoints.length === 0) {
    throw new Error('No waypoints provided');
  }

  if (waypoints.length === 1) {
    // Single destination
    openNavigation(waypoints[0]!, app);
    return;
  }

  // Multiple waypoints - build URL with waypoints
  const origin = waypoints[0]!;
  const destination = waypoints[waypoints.length - 1]!;
  const intermediateWaypoints = waypoints.slice(1, -1);

  if (app === 'google') {
    openGoogleMapsWithWaypoints(origin, destination, intermediateWaypoints);
  } else if (app === 'apple') {
    // Apple Maps doesn't support multiple waypoints in URL scheme
    // Open with just the first destination and show a message
    openNavigation(origin, app);
    
    // Could show a notification that user needs to manually add other stops
    console.warn('Apple Maps does not support multiple waypoints via URL. Opening first destination only.');
  }
}

/**
 * Open Google Maps with multiple waypoints
 */
function openGoogleMapsWithWaypoints(
  origin: NavigationDestination,
  destination: NavigationDestination,
  waypoints: NavigationDestination[]
): void {
  const originStr = origin.address 
    ? encodeURIComponent(origin.address)
    : `${origin.latitude},${origin.longitude}`;
    
  const destinationStr = destination.address
    ? encodeURIComponent(destination.address)
    : `${destination.latitude},${destination.longitude}`;

  let url = `https://maps.google.com/maps?saddr=${originStr}&daddr=${destinationStr}`;

  // Add waypoints
  if (waypoints.length > 0) {
    const waypointStrs = waypoints.map(wp => 
      wp.address 
        ? encodeURIComponent(wp.address)
        : `${wp.latitude},${wp.longitude}`
    );
    url += `&waypoints=${waypointStrs.join('|')}`;
  }

  url += '&dir_action=navigate';

  window.open(url, '_blank');
} 