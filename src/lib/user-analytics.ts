// User analytics and route tracking service

export interface RouteOptimizationRecord {
  id: string;
  userId: string;
  startedAt: Date;
  completedAt?: Date;
  status: 'optimized' | 'in-progress' | 'completed';
  totalStops: number;
  totalDistance: number;
  totalDuration: number; // in minutes
  distanceSaved: number;
  timeSaved: number; // in minutes
  optimizationPercentage: number;
  startingAddress: string;
  destinationAddress: string;
  stops: string[];
}

export interface UserAnalytics {
  totalRoutes: number;
  routesThisMonth: number;
  totalDistanceSaved: number;
  totalTimeSaved: number; // in hours
  averageOptimization: number;
  routeEfficiency: number;
  averageStopsPerRoute: number;
  bestOptimization: number;
  successRate: number;
  recentRoutes: RouteOptimizationRecord[];
}

// Mock data service - in a real app, this would connect to your database
export class UserAnalyticsService {
  // Store routes per user ID - new users will have empty arrays
  private static userRoutes: Map<string, RouteOptimizationRecord[]> = new Map();

  // Initialize with some demo data for a specific demo user (optional)
  private static initializeDemoData() {
    const demoUserId = 'demo-user-with-data';
    if (!this.userRoutes.has(demoUserId)) {
      this.userRoutes.set(demoUserId, [
        {
          id: '1',
          userId: demoUserId,
          startedAt: new Date('2024-01-15T14:30:00'),
          completedAt: new Date('2024-01-15T16:45:00'),
          status: 'completed',
          totalStops: 12,
          totalDistance: 28.1,
          totalDuration: 135, // 2h 15m
          distanceSaved: 8.2,
          timeSaved: 35,
          optimizationPercentage: 25,
          startingAddress: '123 Main St, City, State',
          destinationAddress: '456 Oak Ave, City, State',
          stops: ['789 Pine St', '321 Elm St', '654 Maple Ave', '987 Cedar Ln']
        },
        {
          id: '2',
          userId: demoUserId,
          startedAt: new Date('2024-01-14T09:15:00'),
          status: 'in-progress',
          totalStops: 8,
          totalDistance: 19.9,
          totalDuration: 105, // 1h 45m
          distanceSaved: 4.1,
          timeSaved: 22,
          optimizationPercentage: 18,
          startingAddress: '111 First St, City, State',
          destinationAddress: '222 Second St, City, State',
          stops: ['333 Third St', '444 Fourth St', '555 Fifth St']
        },
        {
          id: '3',
          userId: demoUserId,
          startedAt: new Date('2024-01-13T16:45:00'),
          completedAt: new Date('2024-01-13T17:30:00'),
          status: 'optimized',
          totalStops: 5,
          totalDistance: 11.6,
          totalDuration: 45,
          distanceSaved: 4.8,
          timeSaved: 18,
          optimizationPercentage: 32,
          startingAddress: '777 Seventh St, City, State',
          destinationAddress: '888 Eighth St, City, State',
          stops: ['999 Ninth St', '101 Tenth St']
        }
      ]);
    }
  }

  static async getUserAnalytics(userId: string): Promise<UserAnalytics> {
    // Initialize demo data if needed
    this.initializeDemoData();
    
    // Get user-specific routes (empty array for new users)
    const userRoutes = this.userRoutes.get(userId) || [];
    
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const routesThisMonth = userRoutes.filter(route => {
      const routeDate = new Date(route.startedAt);
      return routeDate.getMonth() === currentMonth && routeDate.getFullYear() === currentYear;
    });

    const totalDistanceSaved = userRoutes.reduce((sum, route) => sum + route.distanceSaved, 0);
    const totalTimeSaved = userRoutes.reduce((sum, route) => sum + route.timeSaved, 0) / 60; // convert to hours
    const averageOptimization = userRoutes.length > 0 
      ? userRoutes.reduce((sum, route) => sum + route.optimizationPercentage, 0) / userRoutes.length 
      : 0;
    
    const completedRoutes = userRoutes.filter(route => route.status === 'completed');
    const successRate = userRoutes.length > 0 ? (completedRoutes.length / userRoutes.length) * 100 : 0;
    
    const averageStopsPerRoute = userRoutes.length > 0 
      ? userRoutes.reduce((sum, route) => sum + route.totalStops, 0) / userRoutes.length 
      : 0;
    
    const bestOptimization = userRoutes.length > 0 
      ? Math.max(...userRoutes.map(route => route.optimizationPercentage)) 
      : 0;

    // Calculate route efficiency (simplified metric)
    const routeEfficiency = averageOptimization > 0 ? Math.min(averageOptimization * 3, 100) : 0;

    return {
      totalRoutes: userRoutes.length,
      routesThisMonth: routesThisMonth.length,
      totalDistanceSaved: Math.round(totalDistanceSaved * 10) / 10,
      totalTimeSaved: Math.round(totalTimeSaved * 10) / 10,
      averageOptimization: Math.round(averageOptimization),
      routeEfficiency: Math.round(routeEfficiency),
      averageStopsPerRoute: Math.round(averageStopsPerRoute * 10) / 10,
      bestOptimization,
      successRate: Math.round(successRate * 10) / 10,
      recentRoutes: userRoutes.slice(0, 10) // Return last 10 routes
    };
  }

  static async getRecentRoutes(userId: string, limit: number = 10): Promise<RouteOptimizationRecord[]> {
    // Initialize demo data if needed
    this.initializeDemoData();
    
    // Get user-specific routes (empty array for new users)
    const userRoutes = this.userRoutes.get(userId) || [];
    
    return userRoutes
      .sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime())
      .slice(0, limit);
  }

  // Method to add a new route optimization record
  static async addRouteRecord(record: Omit<RouteOptimizationRecord, 'id'>): Promise<RouteOptimizationRecord> {
    const newRecord: RouteOptimizationRecord = {
      ...record,
      id: Date.now().toString() // Simple ID generation for mock
    };
    
    // Get or create user's route array
    const userRoutes = this.userRoutes.get(record.userId) || [];
    userRoutes.push(newRecord);
    this.userRoutes.set(record.userId, userRoutes);
    
    return newRecord;
  }

  // Method to update route status
  static async updateRouteStatus(routeId: string, status: RouteOptimizationRecord['status'], completedAt?: Date): Promise<void> {
    // Find the route across all users
    for (const [userId, routes] of this.userRoutes.entries()) {
      const routeIndex = routes.findIndex(route => route.id === routeId);
      if (routeIndex !== -1) {
        const route = routes[routeIndex];
        if (route) {
          route.status = status;
          if (completedAt) {
            route.completedAt = completedAt;
          }
        }
        break;
      }
    }
  }
} 