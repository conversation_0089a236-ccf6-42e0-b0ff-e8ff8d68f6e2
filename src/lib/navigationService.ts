import { openNavigation, openMultiWaypointNavigation, getDefaultNavigationApp, type NavigationApp, type NavigationDestination } from './navigation';

export interface RouteStop {
  id: string;
  address: string;
  latitude: number;
  longitude: number;
  orderIndex: number;
  completed: boolean;
}

export interface NavigationRoute {
  id: string;
  name: string;
  startingAddress: string;
  startingLatitude: number;
  startingLongitude: number;
  destinationAddress?: string;
  destinationLatitude?: number;
  destinationLongitude?: number;
  stops: RouteStop[];
  currentStopIndex: number;
  status: 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';
}

export interface NavigationPreferences {
  navigationApp: 'GOOGLE_MAPS' | 'APPLE_MAPS';
  autoAdvance: boolean;
  showETA: boolean;
}

/**
 * Navigation service for route management and navigation integration
 */
export class NavigationService {
  private static preferences: NavigationPreferences = {
    navigationApp: 'GOOGLE_MAPS',
    autoAdvance: true,
    showETA: true,
  };

  /**
   * Set user navigation preferences
   */
  static setPreferences(preferences: Partial<NavigationPreferences>) {
    this.preferences = { ...this.preferences, ...preferences };
  }

  /**
   * Get current navigation preferences
   */
  static getPreferences(): NavigationPreferences {
    return this.preferences;
  }

  /**
   * Navigate to the next stop in a route
   */
  static navigateToNextStop(route: NavigationRoute): { stopIndex: number; stop: RouteStop } | null {
    const nextStop = this.getNextIncompleteStop(route);
    
    if (!nextStop) {
      return null;
    }

    const destination: NavigationDestination = {
      address: nextStop.address,
      latitude: nextStop.latitude,
      longitude: nextStop.longitude,
    };

    const navApp = this.preferences.navigationApp === 'GOOGLE_MAPS' ? 'google' : 'apple';
    
    try {
      openNavigation(destination, navApp);
      return {
        stopIndex: nextStop.orderIndex,
        stop: nextStop,
      };
    } catch (error) {
      console.error('Navigation error:', error);
      return null;
    }
  }

  /**
   * Navigate to a specific stop
   */
  static navigateToStop(route: NavigationRoute, stopId: string): boolean {
    const stop = route.stops.find(s => s.id === stopId);
    
    if (!stop) {
      return false;
    }

    const destination: NavigationDestination = {
      address: stop.address,
      latitude: stop.latitude,
      longitude: stop.longitude,
    };

    const navApp = this.preferences.navigationApp === 'GOOGLE_MAPS' ? 'google' : 'apple';
    
    try {
      openNavigation(destination, navApp);
      return true;
    } catch (error) {
      console.error('Navigation error:', error);
      return false;
    }
  }

  /**
   * Navigate entire route with all waypoints
   */
  static navigateEntireRoute(route: NavigationRoute): boolean {
    try {
      // Prepare all waypoints
      const waypoints: NavigationDestination[] = [];

      // Add starting point
      waypoints.push({
        address: route.startingAddress,
        latitude: route.startingLatitude,
        longitude: route.startingLongitude,
      });

      // Add incomplete stops in order
      const incompleteStops = route.stops
        .filter(stop => !stop.completed)
        .sort((a, b) => a.orderIndex - b.orderIndex);

      incompleteStops.forEach(stop => {
        waypoints.push({
          address: stop.address,
          latitude: stop.latitude,
          longitude: stop.longitude,
        });
      });

      // Add destination if specified
      if (route.destinationAddress && route.destinationLatitude && route.destinationLongitude) {
        waypoints.push({
          address: route.destinationAddress,
          latitude: route.destinationLatitude,
          longitude: route.destinationLongitude,
        });
      }

      const navApp = this.preferences.navigationApp === 'GOOGLE_MAPS' ? 'google' : 'apple';
      openMultiWaypointNavigation(waypoints, navApp);
      
      return true;
    } catch (error) {
      console.error('Multi-waypoint navigation error:', error);
      return false;
    }
  }

  /**
   * Get the next incomplete stop
   */
  static getNextIncompleteStop(route: NavigationRoute): RouteStop | null {
    const incompleteStops = route.stops
      .filter(stop => !stop.completed)
      .sort((a, b) => a.orderIndex - b.orderIndex);

    return incompleteStops[0] || null;
  }

  /**
   * Get route progress information
   */
  static getRouteProgress(route: NavigationRoute): {
    completedStops: number;
    totalStops: number;
    progressPercentage: number;
    nextStop: RouteStop | null;
    estimatedTimeRemaining?: string;
  } {
    const completedStops = route.stops.filter(stop => stop.completed).length;
    const totalStops = route.stops.length;
    const progressPercentage = totalStops > 0 ? (completedStops / totalStops) * 100 : 0;
    const nextStop = this.getNextIncompleteStop(route);

    return {
      completedStops,
      totalStops,
      progressPercentage,
      nextStop,
      // estimatedTimeRemaining would be calculated based on route data
    };
  }

  /**
   * Check if route is completed
   */
  static isRouteCompleted(route: NavigationRoute): boolean {
    return route.stops.length > 0 && route.stops.every(stop => stop.completed);
  }

  /**
   * Get stops in optimized order
   */
  static getOptimizedStops(route: NavigationRoute): RouteStop[] {
    return [...route.stops].sort((a, b) => a.orderIndex - b.orderIndex);
  }

  /**
   * Get incomplete stops in order
   */
  static getIncompleteStops(route: NavigationRoute): RouteStop[] {
    return route.stops
      .filter(stop => !stop.completed)
      .sort((a, b) => a.orderIndex - b.orderIndex);
  }

  /**
   * Get completed stops
   */
  static getCompletedStops(route: NavigationRoute): RouteStop[] {
    return route.stops
      .filter(stop => stop.completed)
      .sort((a, b) => a.orderIndex - b.orderIndex);
  }

  /**
   * Get navigation app display name
   */
  static getNavigationAppName(): string {
    switch (this.preferences.navigationApp) {
      case 'GOOGLE_MAPS':
        return 'Google Maps';
      case 'APPLE_MAPS':
        return 'Apple Maps';
      default:
        return 'Navigation App';
    }
  }

  /**
   * Auto-detect best navigation app for current platform
   */
  static detectBestNavigationApp(): 'GOOGLE_MAPS' | 'APPLE_MAPS' {
    const defaultApp = getDefaultNavigationApp();
    return defaultApp === 'google' ? 'GOOGLE_MAPS' : 'APPLE_MAPS';
  }

  /**
   * Generate route sharing URL
   */
  static generateShareableRouteUrl(route: NavigationRoute): string {
    // This would generate a shareable URL in a real implementation
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    return `${baseUrl}/route/${route.id}`;
  }

  /**
   * Export route to various formats
   */
  static exportRoute(route: NavigationRoute, format: 'json' | 'csv' | 'gpx'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(route, null, 2);
        
      case 'csv':
        const headers = 'Order,Address,Latitude,Longitude,Completed\n';
        const rows = route.stops
          .sort((a, b) => a.orderIndex - b.orderIndex)
          .map(stop => `${stop.orderIndex + 1},"${stop.address}",${stop.latitude},${stop.longitude},${stop.completed}`)
          .join('\n');
        return headers + rows;
        
      case 'gpx':
        // Simplified GPX export
        const waypoints = route.stops
          .sort((a, b) => a.orderIndex - b.orderIndex)
          .map(stop => `    <wpt lat="${stop.latitude}" lon="${stop.longitude}">
      <name>${stop.address}</name>
    </wpt>`)
          .join('\n');
          
        return `<?xml version="1.0" encoding="UTF-8"?>
<gpx version="1.1" creator="OptiRoutePro">
  <metadata>
    <name>${route.name}</name>
  </metadata>
${waypoints}
</gpx>`;
        
      default:
        return JSON.stringify(route, null, 2);
    }
  }
} 