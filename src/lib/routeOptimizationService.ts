import { geocodeAddresses, type GeocodedAddress } from './geocoding';
import { optimizeWaypointSequence, getOptimizedRoute, formatDistance, formatDuration, type RoutePoint, type OptimizedRoute } from './routing';
import { extractTextFromImage, processMultipleImages, type ParsedAddress } from './imageParser';

export interface RouteOptimizationInput {
  startingAddress?: string;
  destinationAddress?: string;
  stopAddresses?: string[];
  imageFiles?: File[];
  name?: string;
}

export interface RouteOptimizationResult {
  route: {
    name: string;
    startingPoint: RoutePoint & { geocoded: GeocodedAddress };
    destination?: RoutePoint & { geocoded: GeocodedAddress };
    stops: Array<RoutePoint & { geocoded: GeocodedAddress; orderIndex: number }>;
    originalOrder: number[];
    optimizedOrder: number[];
  };
  optimization: {
    totalDistance: number;
    totalDuration: number;
    formattedDistance: string;
    formattedDuration: string;
    savings?: {
      distanceSaved: number;
      timeSaved: number;
      formattedDistanceSaved: string;
      formattedTimeSaved: string;
    };
  };
  metadata: {
    geocodingSuccess: number;
    geocodingTotal: number;
    addressesFromImages?: number;
    imageProcessingSuccess?: number;
  };
}

export interface RouteOptimizationError {
  type: 'GEOCODING_FAILED' | 'NO_ADDRESSES' | 'IMAGE_PROCESSING_FAILED' | 'OPTIMIZATION_FAILED' | 'UNKNOWN';
  message: string;
  details?: any;
}

/**
 * Comprehensive route optimization service
 */
export class RouteOptimizationService {
  /**
   * Optimize route from various input sources
   */
  static async optimizeRoute(input: RouteOptimizationInput): Promise<RouteOptimizationResult | RouteOptimizationError> {
    try {
      // Step 1: Collect all addresses
      const allAddresses = await this.collectAddresses(input);
      
      if (allAddresses.length === 0) {
        return {
          type: 'NO_ADDRESSES',
          message: 'No addresses found to optimize',
        };
      }

      // Step 2: Validate required starting point
      if (!input.startingAddress && allAddresses.length > 0) {
        // Use first address as starting point if not specified
        input.startingAddress = allAddresses[0];
        allAddresses.shift(); // Remove from stops
      }

      if (!input.startingAddress) {
        return {
          type: 'NO_ADDRESSES',
          message: 'Starting address is required',
        };
      }

      // Step 3: Prepare addresses for geocoding
      const addressesToGeocode = [input.startingAddress];
      if (input.destinationAddress) {
        addressesToGeocode.push(input.destinationAddress);
      }
      addressesToGeocode.push(...allAddresses);

      // Step 4: Geocode all addresses
      const geocodingResults = await geocodeAddresses(addressesToGeocode);
      
      // Check geocoding success rate
      const successfulGeocoding = geocodingResults.filter(result => result !== null);
      const geocodingSuccessRate = successfulGeocoding.length / geocodingResults.length;
      
      if (geocodingSuccessRate < 0.7) { // Require at least 70% success rate
        return {
          type: 'GEOCODING_FAILED',
          message: `Geocoding failed for too many addresses. Success rate: ${Math.round(geocodingSuccessRate * 100)}%`,
          details: {
            failed: geocodingResults.length - successfulGeocoding.length,
            total: geocodingResults.length,
          },
        };
      }

      // Step 5: Extract successfully geocoded points
      const [startGeocode, destinationGeocode, ...stopGeocodes] = geocodingResults;
      
      if (!startGeocode) {
        return {
          type: 'GEOCODING_FAILED',
          message: 'Failed to geocode starting address',
        };
      }

      const validStopGeocodes = stopGeocodes.filter(Boolean) as GeocodedAddress[];
      
      if (validStopGeocodes.length === 0) {
        return {
          type: 'NO_ADDRESSES',
          message: 'No valid stop addresses found after geocoding',
        };
      }

      // Step 6: Prepare route points
      const startPoint: RoutePoint = {
        latitude: startGeocode.latitude,
        longitude: startGeocode.longitude,
        address: startGeocode.address,
      };

      const waypoints: RoutePoint[] = validStopGeocodes.map(geocode => ({
        latitude: geocode.latitude,
        longitude: geocode.longitude,
        address: geocode.address,
      }));

      const destinationPoint: RoutePoint | undefined = destinationGeocode ? {
        latitude: destinationGeocode.latitude,
        longitude: destinationGeocode.longitude,
        address: destinationGeocode.address,
      } : undefined;

      // Step 7: Optimize waypoint sequence
      const originalOrder = waypoints.map((_, index) => index);
      const optimizedOrder = await optimizeWaypointSequence(startPoint, waypoints, destinationPoint);
      
      // Step 8: Get detailed route information
      const optimizedRouteInfo = await getOptimizedRoute(startPoint, waypoints, optimizedOrder, destinationPoint);

      // Step 9: Calculate potential savings (compare with original order)
      const originalRouteInfo = await getOptimizedRoute(startPoint, waypoints, originalOrder, destinationPoint);
      
      const savings = {
        distanceSaved: Math.max(0, originalRouteInfo.totalDistance - optimizedRouteInfo.totalDistance),
        timeSaved: Math.max(0, originalRouteInfo.totalDuration - optimizedRouteInfo.totalDuration),
        formattedDistanceSaved: formatDistance(Math.max(0, originalRouteInfo.totalDistance - optimizedRouteInfo.totalDistance)),
        formattedTimeSaved: formatDuration(Math.max(0, originalRouteInfo.totalDuration - optimizedRouteInfo.totalDuration)),
      };

      // Step 10: Prepare result
      const result: RouteOptimizationResult = {
        route: {
          name: input.name || `Route ${new Date().toLocaleDateString()}`,
          startingPoint: { ...startPoint, geocoded: startGeocode },
          destination: destinationPoint ? { ...destinationPoint, geocoded: destinationGeocode! } : undefined,
          stops: optimizedOrder.map((originalIndex, orderIndex) => ({
            ...waypoints[originalIndex]!,
            geocoded: validStopGeocodes[originalIndex]!,
            orderIndex,
          })),
          originalOrder,
          optimizedOrder,
        },
        optimization: {
          totalDistance: optimizedRouteInfo.totalDistance,
          totalDuration: optimizedRouteInfo.totalDuration,
          formattedDistance: formatDistance(optimizedRouteInfo.totalDistance),
          formattedDuration: formatDuration(optimizedRouteInfo.totalDuration),
          savings,
        },
        metadata: {
          geocodingSuccess: successfulGeocoding.length,
          geocodingTotal: geocodingResults.length,
          ...(input.imageFiles && {
            addressesFromImages: allAddresses.length,
            imageProcessingSuccess: input.imageFiles.length,
          }),
        },
      };

      return result;
    } catch (error) {
      console.error('Route optimization error:', error);
      return {
        type: 'UNKNOWN',
        message: 'An unexpected error occurred during route optimization',
        details: error,
      };
    }
  }

  /**
   * Collect addresses from all input sources
   */
  private static async collectAddresses(input: RouteOptimizationInput): Promise<string[]> {
    const addresses: string[] = [];

    // Add manual addresses
    if (input.stopAddresses) {
      addresses.push(...input.stopAddresses);
    }

    // Process images if provided
    if (input.imageFiles && input.imageFiles.length > 0) {
      try {
        const parsedAddresses = await processMultipleImages(input.imageFiles);
        const imageAddresses = parsedAddresses
          .filter(addr => addr.confidence > 0.5) // Only include high-confidence addresses
          .map(addr => addr.address);
        addresses.push(...imageAddresses);
      } catch (error) {
        console.error('Image processing error:', error);
        // Continue with manual addresses even if image processing fails
      }
    }

    // Remove duplicates (case-insensitive)
    const uniqueAddresses = addresses.filter((address, index, arr) => 
      arr.findIndex(a => a.toLowerCase().trim() === address.toLowerCase().trim()) === index
    );

    return uniqueAddresses;
  }

  /**
   * Validate route optimization input
   */
  static validateInput(input: RouteOptimizationInput): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if at least one address source is provided
    const hasManualAddresses = input.stopAddresses && input.stopAddresses.length > 0;
    const hasImages = input.imageFiles && input.imageFiles.length > 0;
    const hasStartingAddress = input.startingAddress && input.startingAddress.trim().length > 0;

    if (!hasManualAddresses && !hasImages && !hasStartingAddress) {
      errors.push('At least one address source must be provided');
    }

    // Validate image files
    if (input.imageFiles) {
      if (input.imageFiles.length > 15) {
        errors.push('Maximum 15 images allowed');
      }

      for (const file of input.imageFiles) {
        if (file.size > 2 * 1024 * 1024) { // 2MB limit
          errors.push(`Image ${file.name} exceeds 2MB size limit`);
        }

        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!validTypes.includes(file.type)) {
          errors.push(`Image ${file.name} has unsupported format. Supported: JPEG, PNG, WebP`);
        }
      }
    }

    // Validate manual addresses
    if (input.stopAddresses) {
      if (input.stopAddresses.length > 50) {
        errors.push('Maximum 50 stops allowed');
      }

      const emptyAddresses = input.stopAddresses.filter(addr => !addr.trim());
      if (emptyAddresses.length > 0) {
        errors.push('Empty addresses are not allowed');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get route optimization status for UI progress tracking
   */
  static async getOptimizationProgress(optimizationId: string): Promise<{
    status: 'processing' | 'completed' | 'failed';
    stage: 'collecting' | 'geocoding' | 'optimizing' | 'saving' | 'done';
    progress: number; // 0-100
    message: string;
  }> {
    // This would be implemented with a job queue in production
    // For now, return a mock status
    return {
      status: 'completed',
      stage: 'done',
      progress: 100,
      message: 'Route optimization completed',
    };
  }
} 