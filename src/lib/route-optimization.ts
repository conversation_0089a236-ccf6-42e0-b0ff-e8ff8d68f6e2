// Types for route optimization
export interface RouteStop {
  address: string; // Original address as entered by user
  geocodedAddress?: string; // Full geocoded address from API
  latitude?: number;
  longitude?: number;
  isCompleted?: boolean;
  originalIndex?: number;
  // Segment info to next stop
  distanceToNext?: number; // in miles
  timeToNext?: number; // in minutes
}

export interface RouteOptimizationRequest {
  startingAddress: string;
  stops: string[];
  destinationAddress: string;
}

export interface RouteData {
  stops: RouteStop[];
  totalDistance: number; // in miles
  totalDuration: number; // in minutes
  polyline?: string; // Encoded polyline for the route
  sections?: any[]; // Route sections with individual polylines
}

export interface OptimizedRoute {
  optimizedStops: RouteStop[];
  totalDistance: number; // in miles
  totalDuration: number; // in minutes
  polyline?: string; // Encoded polyline for optimized route
  sections?: any[]; // Route sections with individual polylines
  savings: {
    distance: number; // in miles
    time: number; // in minutes
    percentage: number;
  };
  provider: 'mapbox' | 'here';
}

// Import geocoding cache
import { GeocodingCache } from './geocoding-cache';

// Geocoding with Mapbox (with caching)
export async function geocodeAddresses(addresses: string[]): Promise<RouteStop[]> {
  try {
    const geocodingCache = GeocodingCache.getInstance();
    const geocodedStops: RouteStop[] = [];
    const addressesToGeocode: { address: string; index: number }[] = [];
    
    // First pass: check cache for all addresses
    for (let i = 0; i < addresses.length; i++) {
      const address = addresses[i];
      if (!address) continue; // Skip empty addresses
      
      const cachedResult = geocodingCache.get(address);
      
      if (cachedResult) {
        // Use cached result
        geocodedStops[i] = {
          address: cachedResult.address,
          geocodedAddress: cachedResult.geocodedAddress,
          latitude: cachedResult.latitude,
          longitude: cachedResult.longitude,
          isCompleted: false,
        };
      } else {
        // Mark for API geocoding
        addressesToGeocode.push({ address, index: i });
        // Placeholder for now
        geocodedStops[i] = {
          address: address,
          isCompleted: false,
        };
      }
    }
    
    // Second pass: geocode uncached addresses via API
    if (addressesToGeocode.length > 0) {
      console.log(`Geocoding ${addressesToGeocode.length} addresses via Mapbox API`);
      
      for (const { address, index } of addressesToGeocode) {
        try {
          const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN}&limit=1`
          );
          
          if (!response.ok) {
            console.warn(`Failed to geocode address: ${address}`);
            continue;
          }
          
          const data = await response.json();
          
          if (data.features && data.features.length > 0) {
            const feature = data.features[0];
            const geocodedResult = {
              address: address, // Keep original address
              geocodedAddress: feature.place_name, // Store full geocoded address
              latitude: feature.center[1],
              longitude: feature.center[0],
              isCompleted: false,
            };
            
            // Update the result in our array
            geocodedStops[index] = geocodedResult;
            
            // Cache the successful result
            geocodingCache.set(address, {
              address: address,
              geocodedAddress: feature.place_name,
              latitude: feature.center[1],
              longitude: feature.center[0],
              source: 'mapbox',
            });
          } else {
            console.warn(`No geocoding results for address: ${address}`);
            // Keep the placeholder with just the original address
          }
        } catch (error) {
          console.error(`Error geocoding address "${address}":`, error);
          // Keep the placeholder with just the original address
        }
      }
    } else {
      console.log('All addresses found in cache, no API calls needed');
    }
    
    return geocodedStops;
  } catch (error) {
    console.error('Geocoding failed:', error);
    throw error;
  }
}

// Calculate original route (in user's order) using HERE API
async function calculateOriginalRoute(geocodedStops: RouteStop[]): Promise<RouteData> {
  const validStops = geocodedStops.filter(stop => stop.latitude && stop.longitude);
  
  if (validStops.length < 2) {
    throw new Error('Need at least 2 valid stops to calculate route');
  }
  
  console.log(`Calculating original route using HERE API for ${validStops.length} stops`);
  
  // Use HERE API for all routes
  const coordinates: [number, number][] = validStops.map(stop => [stop.longitude!, stop.latitude!]);
  const routeData = await getRouteFromHere(coordinates);
  
  return {
    stops: geocodedStops,
    totalDistance: routeData.distance,
    totalDuration: routeData.duration,
    polyline: routeData.polyline,
    sections: routeData.sections
  };
}

// Get route data from HERE API
async function getRouteFromHere(coordinates: [number, number][]): Promise<{ distance: number; duration: number; polyline: string; sections: any[] }> {
  try {
    if (coordinates.length < 2) {
      throw new Error('Need at least 2 coordinates for HERE route calculation');
    }

    const start = coordinates[0];
    const end = coordinates[coordinates.length - 1];
    const waypoints = coordinates.slice(1, -1);

    if (!start || !end) {
      throw new Error('Invalid start or end coordinates');
    }

    // Validate coordinates
    if (!isValidCoordinate(start[1], start[0]) || !isValidCoordinate(end[1], end[0])) {
      throw new Error('Invalid start or end coordinates');
    }

    // Validate waypoints
    for (const wp of waypoints) {
      if (!isValidCoordinate(wp[1], wp[0])) {
        throw new Error('Invalid waypoint coordinates');
      }
    }

    const routingUrl = new URL('https://router.hereapi.com/v8/routes');
    routingUrl.searchParams.append('apikey', process.env.NEXT_PUBLIC_HERE_API_KEY || '');
    routingUrl.searchParams.append('transportMode', 'car');
    routingUrl.searchParams.append('return', 'polyline,summary');
    routingUrl.searchParams.append('units', 'imperial');
    
    // Set origin and destination
    routingUrl.searchParams.append('origin', `${start[1]},${start[0]}`);
    routingUrl.searchParams.append('destination', `${end[1]},${end[0]}`);
    
    // Add via points if any
    if (waypoints.length > 0) {
      waypoints.forEach(wp => {
        routingUrl.searchParams.append('via', `${wp[1]},${wp[0]}`);
      });
    }

    console.log(`HERE API routing request for ${coordinates.length} waypoints`);

    const response = await fetchWithRetry(routingUrl.toString(), {
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('HERE API error details:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
        coordinates: coordinates.length,
        hasWaypoints: waypoints.length > 0
      });
      throw new Error(`HERE API error: ${response.status} - ${errorText}`);
    }
    
    const data = await response.json();
    
    if (!data.routes || data.routes.length === 0) {
      throw new Error('No route found from HERE');
    }
    
    const route = data.routes[0];
    
    // HERE API v8 returns summary at route level, not route.summary
    const summary = route.summary || {};
    const sections = route.sections || [];
    
    // Calculate totals from sections if summary is empty
    let totalDistance = summary.length || 0;
    let totalDuration = summary.duration || 0;
    
    if (totalDistance === 0 && sections.length > 0) {
      totalDistance = sections.reduce((total: number, section: any) => {
        return total + (section.summary?.length || 0);
      }, 0);
      totalDuration = sections.reduce((total: number, section: any) => {
        return total + (section.summary?.duration || 0);
      }, 0);
      console.log('Calculated from sections - Distance:', totalDistance, 'Duration:', totalDuration);
    }
    
    // Combine all section polylines into one
    let combinedPolyline = '';
    if (sections.length > 0) {
      // Combine all section polylines for the complete route
      const allPolylines = sections
        .map((section: any) => section.polyline)
        .filter((polyline: string) => polyline && polyline.length > 0);
      
      if (allPolylines.length > 0) {
        // For HERE API, we'll use the first section's polyline as the main one
        // and log all available polylines for debugging
        combinedPolyline = allPolylines[0];
        console.log(`Found ${allPolylines.length} section polylines, using first one with length:`, combinedPolyline.length);
        console.log('All polyline lengths:', allPolylines.map((p: string) => p.length));
      }
    }
    
    const distanceInMiles = totalDistance ? Math.round((totalDistance / 1609.34) * 100) / 100 : 0;
    const durationInMinutes = totalDuration ? Math.round(totalDuration / 60) : 0;
    
    return {
      distance: distanceInMiles, // Convert meters to miles and round to 2 decimal places
      duration: durationInMinutes, // Convert seconds to minutes
      polyline: combinedPolyline,
      sections: sections
    };
  } catch (error) {
    console.error('Failed to get route from HERE:', error);
    throw error;
  }
}

// Helper function to validate coordinates
function isValidCoordinate(lat: number, lng: number): boolean {
  return (
    typeof lat === 'number' && 
    typeof lng === 'number' && 
    !isNaN(lat) && 
    !isNaN(lng) &&
    lat >= -90 && 
    lat <= 90 && 
    lng >= -180 && 
    lng <= 180
  );
}

// Retry logic for API calls
async function fetchWithRetry(url: string, options: RequestInit, maxRetries = 3): Promise<Response> {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);
      
      // Check for rate limit or auth errors
      if (response.status === 401 || response.status === 429) {
        if (attempt < maxRetries) {
          const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff
          console.log(`API call failed with status ${response.status}, retrying in ${waitTime}ms...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }
      }
      
      return response;
    } catch (error) {
      lastError = error;
      
      if (attempt < maxRetries) {
        const waitTime = Math.pow(2, attempt) * 1000;
        console.log(`API call attempt ${attempt} failed, retrying in ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  throw lastError || new Error(`All ${maxRetries} attempts failed`);
}

// HERE API route optimization using Waypoint Sequence API
async function optimizeWithHere(geocodedStops: RouteStop[], originalRoute: RouteData): Promise<OptimizedRoute> {
  try {
    const validStops = geocodedStops.filter(stop => stop.latitude && stop.longitude);
    
    if (validStops.length < 2) {
      throw new Error('Need at least 2 valid stops for HERE optimization');
    }
    
    // For routes with only 2 stops, no optimization needed
    if (validStops.length === 2) {
      const twoStops = geocodedStops.map((stop, index) => ({ ...stop, originalIndex: index }));
      const twoStopsWithSegments = await calculateSegmentDistances(twoStops);
      
      return {
        optimizedStops: twoStopsWithSegments,
        totalDistance: originalRoute.totalDistance,
        totalDuration: originalRoute.totalDuration,
        polyline: originalRoute.polyline,
        savings: { distance: 0, time: 0, percentage: 0 },
        provider: 'here',
      };
    }
    
    const start = validStops[0];
    const end = validStops[validStops.length - 1];
    const intermediateStops = validStops.slice(1, -1);
    
    if (!start || !end) {
      throw new Error('Start or end point is missing');
    }
    
    console.log(`HERE optimization using Waypoint Sequence API for ${validStops.length} stops`);
    
    // Step 1: Get optimized sequence from HERE Waypoint Sequence API
    const optimizedSequence = await getOptimizedSequenceFromHere(start, end, intermediateStops);
    
    // Step 2: Get detailed route for the optimized sequence
    const optimizedRouteData = await getRouteFromHere(
      optimizedSequence.map(wp => [wp.lng, wp.lat])
    );
    
    // Step 3: Create optimized stops in the new order
    const optimizedStops = optimizedSequence.map((waypoint, index) => {
      const originalStop = geocodedStops.find(stop => 
        Math.abs((stop.latitude || 0) - waypoint.lat) < 0.0001 && 
        Math.abs((stop.longitude || 0) - waypoint.lng) < 0.0001
      );
      
      if (!originalStop) {
        throw new Error(`Could not find original stop for optimized waypoint`);
      }
      
      return {
        ...originalStop,
        originalIndex: geocodedStops.indexOf(originalStop),
      };
    });
    
    // Step 4: Calculate segment distances and times for the optimized route
    console.log('Calculating segment distances for optimized route...');
    const optimizedStopsWithSegments = await calculateSegmentDistances(optimizedStops);
    
    // Calculate savings
    const distanceSaved = originalRoute.totalDistance - optimizedRouteData.distance;
    const timeSaved = originalRoute.totalDuration - optimizedRouteData.duration;
    const percentageSaved = originalRoute.totalDistance > 0 ? 
      Math.round((distanceSaved / originalRoute.totalDistance) * 100) : 0;
    
    console.log(`HERE optimization complete: ${optimizedRouteData.distance.toFixed(1)} miles, ${optimizedRouteData.duration} min, ${percentageSaved}% savings`);
    
    return {
      optimizedStops: optimizedStopsWithSegments,
      totalDistance: optimizedRouteData.distance,
      totalDuration: optimizedRouteData.duration,
      polyline: optimizedRouteData.polyline,
      sections: optimizedRouteData.sections,
      savings: {
        distance: Math.round(distanceSaved * 100) / 100,
        time: timeSaved,
        percentage: Math.max(0, percentageSaved),
      },
      provider: 'here',
    };
  } catch (error) {
    console.error('HERE optimization failed:', error);
    
    // Fallback to original route if optimization fails
    console.log('Falling back to original route order...');
    const fallbackStops = geocodedStops.map((stop, index) => ({ ...stop, originalIndex: index }));
    const fallbackStopsWithSegments = await calculateSegmentDistances(fallbackStops);
    
    return {
      optimizedStops: fallbackStopsWithSegments,
      totalDistance: originalRoute.totalDistance,
      totalDuration: originalRoute.totalDuration,
      polyline: originalRoute.polyline,
      sections: originalRoute.sections,
      savings: { distance: 0, time: 0, percentage: 0 },
      provider: 'here',
    };
  }
}

// Call HERE Waypoint Sequence API for optimization
async function getOptimizedSequenceFromHere(
  start: RouteStop, 
  end: RouteStop, 
  intermediateStops: RouteStop[]
): Promise<Array<{lat: number, lng: number, id: string}>> {
  try {
    const params = new URLSearchParams();
    params.append('apiKey', process.env.NEXT_PUBLIC_HERE_API_KEY || '');
    params.append('start', `${start.latitude},${start.longitude}`);
    params.append('end', `${end.latitude},${end.longitude}`);
    params.append('mode', 'fastest;car;traffic:disabled');
    
    // Add intermediate stops as destinations
    intermediateStops.forEach((stop, index) => {
      params.append(`destination${index}`, `${stop.latitude},${stop.longitude}`);
    });
    
    const sequenceUrl = `https://wse.ls.hereapi.com/2/findsequence.json?${params.toString()}`;
    console.log(`Calling HERE Waypoint Sequence API for ${intermediateStops.length} intermediate stops`);
    
    const response = await fetchWithRetry(sequenceUrl, {
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HERE Waypoint Sequence API error: ${response.status} - ${errorText}`);
    }
    
    const data = await response.json();
    
    if (!data?.results?.[0]?.waypoints) {
      throw new Error('No optimization results from HERE Waypoint Sequence API');
    }
    
    const optimizedWaypoints = data.results[0].waypoints;
    
    // Convert to our format
    return optimizedWaypoints.map((waypoint: any, index: number) => {
      let lat, lng;
      
      if (waypoint.lat !== undefined && waypoint.lng !== undefined) {
        lat = waypoint.lat;
        lng = waypoint.lng;
      } else if (waypoint.mappedPosition) {
        if (typeof waypoint.mappedPosition === 'string') {
          const parts = waypoint.mappedPosition.split(',');
          if (parts.length === 2) {
            lat = parseFloat(parts[0]);
            lng = parseFloat(parts[1]);
          }
        }
      }
      
      if (lat === undefined || lng === undefined || isNaN(lat) || isNaN(lng)) {
        throw new Error(`Invalid coordinates in optimization result at index ${index}`);
      }
      
      return {
        lat,
        lng,
        id: waypoint.id || `waypoint-${index}`
      };
    });
  } catch (error) {
    console.error('HERE Waypoint Sequence API failed:', error);
    throw error;
  }
}

// Main route optimization function
export async function optimizeRoute(request: RouteOptimizationRequest): Promise<OptimizedRoute> {
  try {
    // Prepare all addresses for geocoding
    const allAddresses = [
      request.startingAddress,
      ...request.stops,
      request.destinationAddress,
    ];
    
    console.log(`Starting route optimization for ${allAddresses.length} total waypoints...`);
    
    // Step 1: Geocode all addresses using Mapbox
    console.log('Step 1: Geocoding addresses...');
    const geocodedStops = await geocodeAddresses(allAddresses);
    
    const validStops = geocodedStops.filter(stop => stop.latitude && stop.longitude);
    console.log(`Successfully geocoded ${validStops.length}/${geocodedStops.length} addresses`);
    
    if (validStops.length < 2) {
      throw new Error('Need at least 2 valid addresses to optimize a route');
    }
    
    // Step 2: Calculate original route using HERE API
    console.log('Step 2: Calculating original route...');
    const originalRoute = await calculateOriginalRoute(geocodedStops);
    console.log(`Original route: ${originalRoute.totalDistance.toFixed(1)} miles, ${originalRoute.totalDuration} minutes`);
    
    // Step 3: Optimize route using HERE API
    console.log('Step 3: Optimizing route...');
    return await optimizeWithHere(geocodedStops, originalRoute);
  } catch (error) {
    console.error('Route optimization failed:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('geocod')) {
        throw new Error(`Address geocoding failed: ${error.message}. Please check that your addresses are valid.`);
      } else if (error.message.includes('HERE')) {
        throw new Error(`HERE API error: ${error.message}. Please check your HERE API key and try again.`);
      }
    }
    
    throw error;
  }
}

// Navigation integration
export function openNavigation(stop: RouteStop, navigationApp: 'GOOGLE_MAPS' | 'APPLE_MAPS') {
  if (!stop.latitude || !stop.longitude) {
    console.error('Cannot navigate: No coordinates available for this stop');
    return;
  }
  
  const lat = stop.latitude;
  const lng = stop.longitude;
  const address = encodeURIComponent(stop.geocodedAddress || stop.address);
  
  let navigationURL: string;
  
  switch (navigationApp) {
    case 'GOOGLE_MAPS':
      // Google Maps URL scheme
      navigationURL = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=${address}`;
      break;
    case 'APPLE_MAPS':
      // Apple Maps URL scheme
      navigationURL = `http://maps.apple.com/?daddr=${lat},${lng}&dirflg=d`;
      break;
    default:
      navigationURL = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
  }
  
  // Open in new tab/window or try to open in app
  if (typeof window !== 'undefined') {
    window.open(navigationURL, '_blank');
  }
}

// OpenRouter API for image address parsing
export async function parseAddressesFromImage(imageFile: File): Promise<string[]> {
  try {
    // Check if API key is configured
    const apiKey = process.env.NEXT_PUBLIC_OPENROUTER_API_KEY;
    
    if (!apiKey) {
      console.warn('OpenRouter API key not configured. Using fallback text extraction.');
      // For now, return mock addresses for testing
      return [
        "123 Main Street, Anytown, CA 90210",
        "456 Oak Avenue, Somewhere, CA 90211", 
        "789 Pine Road, Elsewhere, CA 90212"
      ];
    }

    // Convert image to base64
    const base64Image = await fileToBase64(imageFile);
    
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'X-Title': 'OptiRoutePro Address Parser',
      },
      body: JSON.stringify({
        model: 'openai/gpt-4.1-nano',
        messages: [
          {
            role: 'system',
            content: `You are an expert address extraction system. Your job is to find ALL visible addresses in images and return them as a JSON array.

REQUIREMENTS:
1. Extract EVERY address you can see - do not skip any
2. Each address MUST include a city name - if no city is visible, try to infer from context or skip that address
3. Remove exact duplicates automatically
4. Include complex street names like "State Highway 36", "County Road 99B", "Business Route 126"
5. Format: ["123 Main St, Springfield", "456 Oak Ave, Chicago, IL", "789 State Highway 36, Austin"]

EXAMPLES OF GOOD EXTRACTION:
- "123 Main Street, Chicago" ✓
- "456 Oak Ave, Springfield, IL 62701" ✓  
- "789 State Highway 36, Austin, TX" ✓
- "County Road 99B, Denver, CO" ✓

EXAMPLES TO AVOID:
- "123 Main Street" ✗ (missing city)
- Skipping addresses because they're hard to read ✗
- Including the same address twice ✗

OUTPUT: Return ONLY a JSON array of strings. No other text.`
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: 'Find ALL addresses in this image. Each address must include the city name. Remove any duplicates. Return as JSON array only - no other text.'
              },
              {
                type: 'image_url',
                image_url: {
                  url: base64Image
                }
              }
            ]
          }
        ],
        temperature: 0.0, // More deterministic for parsing
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('OpenRouter API error:', response.status, errorData);
      throw new Error(`API request failed: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No content returned from API');
    }
    
    try {
      // Parse the JSON response
      const addresses = JSON.parse(content);
      return Array.isArray(addresses) ? addresses : [];
    } catch {
      // If JSON parsing fails, try to extract addresses from text
      return extractAddressesFromText(content);
    }
  } catch (error) {
    console.error('Image parsing failed:', error);
    
    // Provide user-friendly error message
    if (error instanceof Error) {
      if (error.message.includes('API request failed')) {
        throw new Error('Unable to process image. Please check your internet connection and try again.');
      } else if (error.message.includes('API key')) {
        throw new Error('Image processing service is not configured. Please contact support.');
      }
    }
    
    throw new Error('Failed to extract addresses from image. Please try manually entering the addresses.');
  }
}

// Helper function to convert file to base64
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
}

// Fallback address extraction from text
function extractAddressesFromText(text: string): string[] {
  // Simple regex patterns for address extraction
  const addressPatterns = [
    /\d+\s+[\w\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Court|Ct|Place|Pl),?\s*[\w\s]+,?\s*[A-Z]{2}\s*\d{5}/gi,
    /\d+\s+[\w\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Court|Ct|Place|Pl)[,\s]+[\w\s]+/gi,
  ];

  const addresses: string[] = [];
  
  for (const pattern of addressPatterns) {
    const matches = text.match(pattern);
    if (matches) {
      addresses.push(...matches);
    }
  }

  // Remove duplicates and clean up
  return [...new Set(addresses)].map(addr => addr.trim());
}

// Calculate segment distances and times between consecutive stops
async function calculateSegmentDistances(stops: RouteStop[]): Promise<RouteStop[]> {
  const stopsWithSegments = [...stops];
  
  for (let i = 0; i < stopsWithSegments.length - 1; i++) {
    const currentStop = stopsWithSegments[i];
    const nextStop = stopsWithSegments[i + 1];
    
    if (currentStop && nextStop && 
        currentStop.latitude && currentStop.longitude &&
        nextStop.latitude && nextStop.longitude) {
      
      try {
        // Calculate distance and time to next stop using HERE API
        const segmentData = await getRouteFromHere([
          [currentStop.longitude, currentStop.latitude],
          [nextStop.longitude, nextStop.latitude]
        ]);
        
        stopsWithSegments[i] = {
          ...currentStop,
          distanceToNext: segmentData.distance,
          timeToNext: segmentData.duration
        };
      } catch (error) {
        console.warn(`Failed to calculate segment distance from stop ${i} to ${i + 1}:`, error);
        // Fallback to straight-line distance estimation
        const distance = calculateStraightLineDistance(
          currentStop.latitude, currentStop.longitude,
          nextStop.latitude, nextStop.longitude
        );
        stopsWithSegments[i] = {
          ...currentStop,
          distanceToNext: distance,
          timeToNext: Math.round(distance * 2) // Rough estimate: 2 minutes per mile
        };
      }
    }
  }
  
  return stopsWithSegments;
}

// Calculate straight-line distance between two points (Haversine formula)
function calculateStraightLineDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return Math.round((R * c) * 100) / 100; // Round to 2 decimal places
}