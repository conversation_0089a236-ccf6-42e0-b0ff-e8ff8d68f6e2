export type Language = 'en' | 'ru';

export interface Translations {
  // Navigation
  nav: {
    home: string;
    pricing: string;
    dashboard: string;
    signIn: string;
    signUp: string;
    signOut: string;
  };
  
  // Landing page
  landing: {
    title: string;
    subtitle: string;
    description: string;
    getStarted: string;
    freeTrial: string;
    features: {
      geocoding: string;
      optimization: string;
      navigation: string;
      imageUpload: string;
    };
  };
  
  // Pricing
  pricing: {
    title: string;
    subtitle: string;
    pro: {
      title: string;
      price: string;
      features: string[];
      cta: string;
    };
    unlimited: {
      title: string;
      price: string;
      features: string[];
      cta: string;
    };
    freeTrial: string;
  };
  
  // Dashboard
  dashboard: {
    title: string;
    currentPlan: string;
    usage: string;
    preferences: string;
    newRoute: string;
    routes: string;
    settings: string;
  };
  
  // Route optimization
  route: {
    title: string;
    startingPoint: string;
    destination: string;
    addStops: string;
    uploadImages: string;
    optimizeRoute: string;
    optimizing: string;
    optimized: string;
    completed: string;
    navigate: string;
    done: string;
    finishRoute: string;
    distance: string;
    duration: string;
    stops: string;
  };
  
  // Settings
  settings: {
    title: string;
    theme: string;
    language: string;
    navigationApp: string;
    light: string;
    dark: string;
    system: string;
    english: string;
    russian: string;
    googleMaps: string;
    appleMaps: string;
    save: string;
  };
  
  // Common
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    save: string;
    delete: string;
    edit: string;
    close: string;
    back: string;
    next: string;
    finish: string;
  };
}

const translations: Record<Language, Translations> = {
  en: {
    nav: {
      home: 'Home',
      pricing: 'Pricing',
      dashboard: 'Dashboard',
      signIn: 'Sign In',
      signUp: 'Sign Up',
      signOut: 'Sign Out',
    },
    landing: {
      title: 'OptiRoutePro',
      subtitle: 'Optimize Your Routes, Save Time & Money',
      description: 'Upload route lists via images or text, get optimized routes with turn-by-turn navigation. Save hours of driving time every day.',
      getStarted: 'Get Started',
      freeTrial: '7-day free trial',
      features: {
        geocoding: 'Smart address geocoding with Mapbox',
        optimization: 'Route optimization with HERE API',
        navigation: 'Integration with Google Maps & Apple Maps',
        imageUpload: 'Upload route lists via images (OCR)',
      },
    },
    pricing: {
      title: 'Choose Your Plan',
      subtitle: 'Start with a 7-day free trial',
      pro: {
        title: 'Pro',
        price: '$9/month',
        features: [
          'Up to 50 routes per month',
          'Up to 20 stops per route',
          'Image upload (OCR)',
          'Google Maps & Apple Maps integration',
          'Email support',
        ],
        cta: 'Start Free Trial',
      },
      unlimited: {
        title: 'Unlimited',
        price: '$29/month',
        features: [
          'Unlimited routes',
          'Unlimited stops',
          'Image upload (OCR)',
          'Google Maps & Apple Maps integration',
          'Priority support',
          'Advanced analytics',
        ],
        cta: 'Start Free Trial',
      },
      freeTrial: '7-day free trial included',
    },
    dashboard: {
      title: 'Dashboard',
      currentPlan: 'Current Plan',
      usage: 'Usage',
      preferences: 'Preferences',
      newRoute: 'New Route',
      routes: 'Routes',
      settings: 'Settings',
    },
    route: {
      title: 'Route Optimization',
      startingPoint: 'Starting Point',
      destination: 'Destination (Optional)',
      addStops: 'Add Stops',
      uploadImages: 'Upload Images',
      optimizeRoute: 'Optimize Route',
      optimizing: 'Optimizing...',
      optimized: 'Route Optimized',
      completed: 'Completed',
      navigate: 'Navigate',
      done: 'Done',
      finishRoute: 'Finish Route',
      distance: 'Distance',
      duration: 'Duration',
      stops: 'Stops',
    },
    settings: {
      title: 'Settings',
      theme: 'Theme',
      language: 'Language',
      navigationApp: 'Navigation App',
      light: 'Light',
      dark: 'Dark',
      system: 'System',
      english: 'English',
      russian: 'Russian',
      googleMaps: 'Google Maps',
      appleMaps: 'Apple Maps',
      save: 'Save',
    },
    common: {
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save',
      delete: 'Delete',
      edit: 'Edit',
      close: 'Close',
      back: 'Back',
      next: 'Next',
      finish: 'Finish',
    },
  },
  ru: {
    nav: {
      home: 'Главная',
      pricing: 'Тарифы',
      dashboard: 'Панель',
      signIn: 'Войти',
      signUp: 'Регистрация',
      signOut: 'Выйти',
    },
    landing: {
      title: 'OptiRoutePro',
      subtitle: 'Оптимизируйте маршруты, экономьте время и деньги',
      description: 'Загружайте списки маршрутов через изображения или текст, получайте оптимизированные маршруты с пошаговой навигацией. Экономьте часы времени в дороге каждый день.',
      getStarted: 'Начать',
      freeTrial: '7 дней бесплатно',
      features: {
        geocoding: 'Умное геокодирование адресов с Mapbox',
        optimization: 'Оптимизация маршрутов с HERE API',
        navigation: 'Интеграция с Google Maps и Apple Maps',
        imageUpload: 'Загрузка списков маршрутов через изображения (OCR)',
      },
    },
    pricing: {
      title: 'Выберите тариф',
      subtitle: 'Начните с 7-дневного бесплатного периода',
      pro: {
        title: 'Про',
        price: '$9/месяц',
        features: [
          'До 50 маршрутов в месяц',
          'До 20 остановок на маршрут',
          'Загрузка изображений (OCR)',
          'Интеграция с Google Maps и Apple Maps',
          'Поддержка по email',
        ],
        cta: 'Начать бесплатный период',
      },
      unlimited: {
        title: 'Безлимитный',
        price: '$29/месяц',
        features: [
          'Неограниченные маршруты',
          'Неограниченные остановки',
          'Загрузка изображений (OCR)',
          'Интеграция с Google Maps и Apple Maps',
          'Приоритетная поддержка',
          'Расширенная аналитика',
        ],
        cta: 'Начать бесплатный период',
      },
      freeTrial: '7 дней бесплатно включено',
    },
    dashboard: {
      title: 'Панель управления',
      currentPlan: 'Текущий тариф',
      usage: 'Использование',
      preferences: 'Настройки',
      newRoute: 'Новый маршрут',
      routes: 'Маршруты',
      settings: 'Настройки',
    },
    route: {
      title: 'Оптимизация маршрута',
      startingPoint: 'Начальная точка',
      destination: 'Место назначения (Опционально)',
      addStops: 'Добавить остановки',
      uploadImages: 'Загрузить изображения',
      optimizeRoute: 'Оптимизировать маршрут',
      optimizing: 'Оптимизация...',
      optimized: 'Маршрут оптимизирован',
      completed: 'Завершено',
      navigate: 'Навигация',
      done: 'Готово',
      finishRoute: 'Завершить маршрут',
      distance: 'Расстояние',
      duration: 'Время в пути',
      stops: 'Остановки',
    },
    settings: {
      title: 'Настройки',
      theme: 'Тема',
      language: 'Язык',
      navigationApp: 'Приложение навигации',
      light: 'Светлая',
      dark: 'Тёмная',
      system: 'Системная',
      english: 'Английский',
      russian: 'Русский',
      googleMaps: 'Google Maps',
      appleMaps: 'Apple Maps',
      save: 'Сохранить',
    },
    common: {
      loading: 'Загрузка...',
      error: 'Ошибка',
      success: 'Успешно',
      cancel: 'Отмена',
      confirm: 'Подтвердить',
      save: 'Сохранить',
      delete: 'Удалить',
      edit: 'Редактировать',
      close: 'Закрыть',
      back: 'Назад',
      next: 'Далее',
      finish: 'Завершить',
    },
  },
};

export function getTranslations(language: Language): Translations {
  return translations[language];
}

export function getBrowserLanguage(): Language {
  if (typeof navigator === 'undefined') return 'en';
  
  const language = navigator.language.toLowerCase();
  
  if (language.startsWith('ru')) {
    return 'ru';
  }
  
  return 'en';
} 