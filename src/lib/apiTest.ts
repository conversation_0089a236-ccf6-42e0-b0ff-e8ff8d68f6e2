import { RouteOptimizationService, type RouteOptimizationInput } from './routeOptimizationService';
import { geocodeAddress } from './geocoding';
import { extractAddressesFromText } from './imageParser';

/**
 * API Testing utility for route optimization services
 */
export class APITestSuite {
  /**
   * Test geocoding functionality
   */
  static async testGeocoding(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('Testing geocoding...');
      
      const testAddresses = [
        '1600 Amphitheatre Parkway, Mountain View, CA',
        'Times Square, New York, NY',
        '350 5th Ave, New York, NY 10118', // Empire State Building
      ];

      const results = [];
      
      for (const address of testAddresses) {
        const result = await geocodeAddress(address);
        results.push({ address, result });
        console.log(`Geocoded: ${address} -> ${result?.address || 'FAILED'}`);
      }

      const successCount = results.filter(r => r.result !== null).length;
      const successRate = (successCount / results.length) * 100;

      return {
        success: successRate >= 70,
        message: `Geocoding test completed. Success rate: ${successRate.toFixed(1)}% (${successCount}/${results.length})`,
        data: results,
      };
    } catch (error) {
      return {
        success: false,
        message: `Geocoding test failed: ${error}`,
      };
    }
  }

  /**
   * Test image text extraction
   */
  static async testImageProcessing(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('Testing image processing...');
      
      // Test with sample text that would come from OCR
      const sampleTexts = [
        `
        Delivery Route List
        1. 123 Main Street, Springfield, IL 62701
        2. 456 Oak Avenue, Chicago, IL 60601
        3. 789 Pine Road, Naperville, IL 60540
        4. 321 Elm Drive, Peoria, IL 61602
        `,
        `
        Customer Addresses:
        John Smith - 555 Broadway Ave, New York, NY 10012
        Jane Doe - 888 Market St, San Francisco, CA 94102
        Bob Johnson - 777 State Street, Boston, MA 02101
        `,
      ];

      const results = [];
      
      for (const text of sampleTexts) {
        const addresses = extractAddressesFromText(text);
        results.push({ text: text.trim(), addresses });
        console.log(`Extracted ${addresses.length} addresses from text`);
      }

      const totalAddresses = results.reduce((sum, r) => sum + r.addresses.length, 0);

      return {
        success: totalAddresses > 0,
        message: `Image processing test completed. Extracted ${totalAddresses} addresses total.`,
        data: results,
      };
    } catch (error) {
      return {
        success: false,
        message: `Image processing test failed: ${error}`,
      };
    }
  }

  /**
   * Test route optimization end-to-end
   */
  static async testRouteOptimization(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('Testing route optimization...');
      
      const testInput: RouteOptimizationInput = {
        name: 'Test Route',
        startingAddress: '1600 Amphitheatre Parkway, Mountain View, CA',
        destinationAddress: '1 Hacker Way, Menlo Park, CA',
        stopAddresses: [
          '1 Apple Park Way, Cupertino, CA',
          '410 Terry Ave N, Seattle, WA',
          '1901 3rd Ave, Seattle, WA',
          '2000 N Forest Rd, Getzville, NY',
        ],
      };

      console.log('Validating input...');
      const validation = RouteOptimizationService.validateInput(testInput);
      
      if (!validation.valid) {
        return {
          success: false,
          message: `Input validation failed: ${validation.errors.join(', ')}`,
        };
      }

      console.log('Running optimization...');
      const result = await RouteOptimizationService.optimizeRoute(testInput);
      
      if ('type' in result) {
        // Error case
        return {
          success: false,
          message: `Optimization failed: ${result.message}`,
          data: result,
        };
      }

      console.log('Optimization completed successfully!');
      console.log(`Total distance: ${result.optimization.formattedDistance}`);
      console.log(`Total duration: ${result.optimization.formattedDuration}`);
      console.log(`Stops optimized: ${result.route.stops.length}`);

      return {
        success: true,
        message: `Route optimization completed successfully. Distance: ${result.optimization.formattedDistance}, Duration: ${result.optimization.formattedDuration}`,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: `Route optimization test failed: ${error}`,
      };
    }
  }

  /**
   * Test with mixed input sources (manual + OCR text)
   */
  static async testMixedInputOptimization(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('Testing mixed input optimization...');
      
      // Create mock file objects for testing
      const mockImageTexts = [
        `
        Customer List
        Apple Store - 1 Stockton St, San Francisco, CA 94108
        Google Office - 345 Spear St, San Francisco, CA 94105
        `,
        `
        Additional Deliveries:
        123 Mission St, San Francisco, CA 94103
        456 Folsom St, San Francisco, CA 94105
        `,
      ];

      // Extract addresses from mock image texts
      const allImageAddresses: string[] = [];
      for (const text of mockImageTexts) {
        const addresses = extractAddressesFromText(text);
        allImageAddresses.push(...addresses.map(addr => addr.address));
      }

      const testInput: RouteOptimizationInput = {
        name: 'Mixed Input Test Route',
        startingAddress: 'Pier 39, San Francisco, CA',
        stopAddresses: [
          // Manual addresses
          'Golden Gate Bridge, San Francisco, CA',
          'Lombard Street, San Francisco, CA',
          // Add image-extracted addresses
          ...allImageAddresses,
        ],
      };

      console.log(`Testing with ${testInput.stopAddresses?.length} total addresses`);
      
      const result = await RouteOptimizationService.optimizeRoute(testInput);
      
      if ('type' in result) {
        return {
          success: false,
          message: `Mixed input optimization failed: ${result.message}`,
          data: result,
        };
      }

      return {
        success: true,
        message: `Mixed input optimization completed. ${result.route.stops.length} stops optimized.`,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: `Mixed input test failed: ${error}`,
      };
    }
  }

  /**
   * Run comprehensive test suite
   */
  static async runFullTestSuite(): Promise<{ 
    overall: boolean; 
    results: Array<{ test: string; success: boolean; message: string; data?: any }> 
  }> {
    console.log('=== Starting OptiRoutePro API Test Suite ===');
    
    const tests = [
      { name: 'Geocoding', test: this.testGeocoding },
      { name: 'Image Processing', test: this.testImageProcessing },
      { name: 'Route Optimization', test: this.testRouteOptimization },
      { name: 'Mixed Input Optimization', test: this.testMixedInputOptimization },
    ];

    const results = [];
    let allPassed = true;

    for (const { name, test } of tests) {
      console.log(`\n--- Running ${name} Test ---`);
      
      try {
        const result = await test.call(this);
        results.push({ test: name, ...result });
        
        if (!result.success) {
          allPassed = false;
        }
        
        console.log(`${name}: ${result.success ? 'PASSED' : 'FAILED'} - ${result.message}`);
      } catch (error) {
        const errorResult = {
          test: name,
          success: false,
          message: `Test execution failed: ${error}`,
        };
        results.push(errorResult);
        allPassed = false;
        console.log(`${name}: FAILED - ${errorResult.message}`);
      }
    }

    console.log('\n=== Test Suite Complete ===');
    console.log(`Overall Result: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
    
    const passedCount = results.filter(r => r.success).length;
    console.log(`Summary: ${passedCount}/${results.length} tests passed`);

    return {
      overall: allPassed,
      results,
    };
  }

  /**
   * Test API response times
   */
  static async testPerformance(): Promise<{ success: boolean; message: string; data?: any }> {
    console.log('Testing API performance...');
    
    const performanceTests = [
      {
        name: 'Single Address Geocoding',
        test: () => geocodeAddress('1600 Amphitheatre Parkway, Mountain View, CA'),
      },
      {
        name: 'Text Address Extraction',
        test: () => extractAddressesFromText('123 Main St, Springfield, IL 62701\n456 Oak Ave, Chicago, IL 60601'),
      },
    ];

    const results = [];

    for (const { name, test } of performanceTests) {
      const startTime = Date.now();
      
      try {
        await test();
        const duration = Date.now() - startTime;
        results.push({ test: name, duration, success: true });
        console.log(`${name}: ${duration}ms`);
      } catch (error) {
        const duration = Date.now() - startTime;
        results.push({ test: name, duration, success: false, error });
        console.log(`${name}: FAILED after ${duration}ms`);
      }
    }

    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    const allSucceeded = results.every(r => r.success);

    return {
      success: allSucceeded && avgDuration < 5000, // Success if all pass and avg < 5 seconds
      message: `Performance test completed. Average duration: ${avgDuration.toFixed(0)}ms`,
      data: results,
    };
  }
}

/**
 * Quick test runner for development
 */
export async function quickTest() {
  console.log('Running quick API test...');
  
  try {
    const result = await APITestSuite.testRouteOptimization();
    console.log('Quick test result:', result);
    return result;
  } catch (error) {
    console.error('Quick test failed:', error);
    return { success: false, message: `Quick test failed: ${error}` };
  }
} 