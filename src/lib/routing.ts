export interface RoutePoint {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface OptimizedRoute {
  optimizedOrder: number[]; // Indices of the original stops in optimized order
  totalDistance: number; // in meters
  totalDuration: number; // in seconds
  legs: RouteLeg[];
}

export interface RouteLeg {
  startIndex: number;
  endIndex: number;
  distance: number; // in meters
  duration: number; // in seconds
  polyline: string; // Encoded polyline for the route
}

export interface HereWaypointSequenceResponse {
  results: Array<{
    waypoints: Array<{
      id: string;
      lat: number;
      lng: number;
      sequence: number;
    }>;
    distance: number;
    time: number;
  }>;
}

export interface HereRoutingResponse {
  routes: Array<{
    sections: Array<{
      id: string;
      type: string;
      departure: {
        time: string;
        place: {
          type: string;
          location: {
            lat: number;
            lng: number;
          };
        };
      };
      arrival: {
        time: string;
        place: {
          type: string;
          location: {
            lat: number;
            lng: number;
          };
        };
      };
      summary: {
        length: number;
        duration: number;
      };
      polyline: string;
    }>;
  }>;
}

/**
 * Optimize the order of waypoints using HERE Waypoint Sequence API
 */
export async function optimizeWaypointSequence(
  start: RoutePoint,
  waypoints: RoutePoint[],
  destination?: RoutePoint
): Promise<number[]> {
  const hereApiKey = process.env.HERE_API_KEY;
  
  if (!hereApiKey) {
    throw new Error('HERE API key is not configured');
  }

  if (waypoints.length === 0) {
    return [];
  }

  if (waypoints.length === 1) {
    return [0];
  }

  try {
    // Build the request URL
    const baseUrl = 'https://wps.hereapi.com/v8/findsequence';
    const params = new URLSearchParams({
      apikey: hereApiKey,
      start: `${start.latitude},${start.longitude}`,
      mode: 'fastest;car;traffic:disabled',
    });

    // Add destination if provided
    if (destination) {
      params.append('end', `${destination.latitude},${destination.longitude}`);
    }

    // Add waypoints
    waypoints.forEach((point, index) => {
      params.append(`destination${index + 1}`, `${point.latitude},${point.longitude}`);
    });

    const response = await fetch(`${baseUrl}?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error(`HERE API error: ${response.status} ${response.statusText}`);
    }

    const data: HereWaypointSequenceResponse = await response.json();
    
    if (!data.results || data.results.length === 0) {
      // Fallback to original order if optimization fails
      return waypoints.map((_, index) => index);
    }

    const result = data.results[0];
    if (!result) {
      return waypoints.map((_, index) => index);
    }

    // Extract optimized sequence (excluding start and end points)
    const optimizedSequence = result.waypoints
      .filter(wp => wp.id.startsWith('destination'))
      .sort((a, b) => a.sequence - b.sequence)
      .map(wp => parseInt(wp.id.replace('destination', '')) - 1);

    return optimizedSequence;
  } catch (error) {
    console.error('Waypoint optimization error:', error);
    // Return original order as fallback
    return waypoints.map((_, index) => index);
  }
}

/**
 * Get detailed routing information for the optimized route
 */
export async function getOptimizedRoute(
  start: RoutePoint,
  waypoints: RoutePoint[],
  optimizedOrder: number[],
  destination?: RoutePoint
): Promise<OptimizedRoute> {
  const hereApiKey = process.env.HERE_API_KEY;
  
  if (!hereApiKey) {
    throw new Error('HERE API key is not configured');
  }

  try {
    // Build the route with optimized waypoint order
    const orderedWaypoints = optimizedOrder.map(index => waypoints[index]);
    const allPoints = [start, ...orderedWaypoints];
    
    if (destination) {
      allPoints.push(destination);
    }

    const legs: RouteLeg[] = [];
    let totalDistance = 0;
    let totalDuration = 0;

    // Get routing information for each leg
    for (let i = 0; i < allPoints.length - 1; i++) {
      const origin = allPoints[i];
      const dest = allPoints[i + 1];
      
      if (!origin || !dest) continue;

      const legInfo = await getRouteBetweenPoints(origin, dest);
      
      if (legInfo) {
        legs.push({
          startIndex: i,
          endIndex: i + 1,
          distance: legInfo.distance,
          duration: legInfo.duration,
          polyline: legInfo.polyline,
        });
        
        totalDistance += legInfo.distance;
        totalDuration += legInfo.duration;
      }
    }

    return {
      optimizedOrder,
      totalDistance,
      totalDuration,
      legs,
    };
  } catch (error) {
    console.error('Route calculation error:', error);
    throw new Error('Failed to calculate optimized route');
  }
}

/**
 * Get routing information between two points
 */
async function getRouteBetweenPoints(
  origin: RoutePoint,
  destination: RoutePoint
): Promise<{ distance: number; duration: number; polyline: string } | null> {
  const hereApiKey = process.env.HERE_API_KEY;
  
  if (!hereApiKey) {
    throw new Error('HERE API key is not configured');
  }

  try {
    const baseUrl = 'https://router.hereapi.com/v8/routes';
    const params = new URLSearchParams({
      apikey: hereApiKey,
      transportMode: 'car',
      origin: `${origin.latitude},${origin.longitude}`,
      destination: `${destination.latitude},${destination.longitude}`,
      return: 'summary,polyline',
    });

    const response = await fetch(`${baseUrl}?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error(`HERE API error: ${response.status} ${response.statusText}`);
    }

    const data: HereRoutingResponse = await response.json();
    
    if (!data.routes || data.routes.length === 0) {
      return null;
    }

    const route = data.routes[0];
    if (!route || !route.sections || route.sections.length === 0) {
      return null;
    }

    const section = route.sections[0];
    if (!section) {
      return null;
    }

    return {
      distance: section.summary.length,
      duration: section.summary.duration,
      polyline: section.polyline,
    };
  } catch (error) {
    console.error('Routing error:', error);
    return null;
  }
}

/**
 * Format distance for display
 */
export function formatDistance(meters: number): string {
  if (meters < 1000) {
    return `${Math.round(meters)} m`;
  }
  
  const kilometers = meters / 1000;
  return `${kilometers.toFixed(1)} km`;
}

/**
 * Format duration for display
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)} sec`;
  }
  
  if (seconds < 3600) {
    const minutes = Math.round(seconds / 60);
    return `${minutes} min`;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.round((seconds % 3600) / 60);
  
  if (minutes === 0) {
    return `${hours} hr`;
  }
  
  return `${hours} hr ${minutes} min`;
} 