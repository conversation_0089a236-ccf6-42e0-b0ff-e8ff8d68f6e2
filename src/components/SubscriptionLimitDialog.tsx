"use client";

import { <PERSON>, AlertTriangle, TrendingUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useUpgradeToProDialog } from "@/hooks/useUpgradeToProDialog";
import { PlanTier, type LimitCheckResult } from "@/lib/subscription-limits";

interface SubscriptionLimitDialogProps {
  isOpen: boolean;
  onClose: () => void;
  limitResult: LimitCheckResult;
  planTier: PlanTier;
  feature?: string;
}

export function SubscriptionLimitDialog({
  isOpen,
  onClose,
  limitResult,
  planTier,
  feature,
}: SubscriptionLimitDialogProps) {
  const { openUpgradeDialog } = useUpgradeToProDialog();

  // Early return if limitResult is null to prevent errors
  if (!limitResult) {
    return null;
  }

  const handleUpgrade = () => {
    onClose();
    openUpgradeDialog();
  };

  const getPlanBadgeColor = (tier: PlanTier) => {
    switch (tier) {
      case PlanTier.TRIAL:
        return "orange";
      case PlanTier.PRO:
        return "blue";
      case PlanTier.UNLIMITED:
        return "purple";
      default:
        return "gray";
    }
  };

  const getUpgradeTarget = (tier: PlanTier) => {
    switch (tier) {
      case PlanTier.TRIAL:
        return "Pro";
      case PlanTier.PRO:
        return "Unlimited";
      default:
        return "Pro";
    }
  };

  const usagePercentage = limitResult.currentUsage && limitResult.limit 
    ? Math.min((limitResult.currentUsage / limitResult.limit) * 100, 100)
    : 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
              <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <DialogTitle>Plan Limit Reached</DialogTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge color={getPlanBadgeColor(planTier)} variant="outline">
                  {planTier} Plan
                </Badge>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          <DialogDescription className="text-base">
            {limitResult.reason}
          </DialogDescription>

          {limitResult.currentUsage !== undefined && limitResult.limit !== undefined && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Current Usage</span>
                <span className="font-medium">
                  {limitResult.currentUsage} / {limitResult.limit === Infinity ? '∞' : limitResult.limit}
                </span>
              </div>
              {limitResult.limit !== Infinity && (
                <Progress value={usagePercentage} className="h-2" />
              )}
            </div>
          )}

          {limitResult.upgradeRequired && (
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20">
              <div className="flex items-start gap-3">
                <Crown className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Upgrade to {getUpgradeTarget(planTier)}
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    {planTier === PlanTier.TRIAL ? (
                      "Upgrade to Pro for 50 routes/month, 55 stops/route, and 500 image uploads."
                    ) : (
                      "Upgrade to Unlimited for unlimited routes, 100 stops/route, and unlimited image uploads."
                    )}
                  </p>
                  <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
                    <TrendingUp className="h-4 w-4" />
                    <span>
                      {planTier === PlanTier.TRIAL 
                        ? "50 routes/month • 55 stops/route • 500 image uploads"
                        : "Unlimited routes • 100 stops/route • Unlimited images"
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose}>
            Continue with {planTier}
          </Button>
          {limitResult.upgradeRequired && (
            <Button onClick={handleUpgrade} className="gap-2">
              <Crown className="h-4 w-4" />
              Upgrade Now
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 