"use client";

import { MapPin, Twitter, Linkedin, Github, Mail, Heart } from "lucide-react";
import { PrefetchLink } from "@/components/PrefetchLink";

export function DashboardFooter() {
  const footerLinks = {
    Product: [
      { name: "Dashboard", href: "/routes" },
      { name: "Analytics", href: "/analytics" },
      { name: "Settings", href: "/settings" },
      { name: "API Docs", href: "/api" },
    ],
    Support: [
      { name: "Help Center", href: "/help" },
      { name: "Contact Support", href: "/contact" },
      { name: "Feature Requests", href: "/feedback" },
      { name: "System Status", href: "/status" },
    ],
    Company: [
      { name: "About Us", href: "/about" },
      { name: "Blog", href: "/blog" },
      { name: "Careers", href: "/careers" },
      { name: "Press Kit", href: "/press" },
    ],
    Legal: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
      { name: "Security", href: "/security" },
      { name: "Cookie Policy", href: "/cookies" },
    ],
  };

  const socialLinks = [
    { name: "Twitter", href: "#", icon: Twitter },
    { name: "LinkedIn", href: "#", icon: Linkedin },
    { name: "GitHub", href: "#", icon: Github },
    { name: "Email", href: "mailto:<EMAIL>", icon: Mail },
  ];

  // Dashboard pages that should use prefetching
  const dashboardPages = ["/routes", "/analytics", "/settings"];

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-5">
          {/* Brand */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <MapPin className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">OptiRoute Pro</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Optimize your delivery routes with AI-powered route planning and real-time analytics.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((link) => {
                const Icon = link.icon;
                return (
                  <a
                    key={link.name}
                    href={link.href}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                    aria-label={link.name}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="font-semibold mb-4">{category}</h3>
              <ul className="space-y-2">
                {links.map((link) => {
                  // Use PrefetchLink for dashboard pages, regular link for others
                  const isDashboardPage = dashboardPages.includes(link.href);
                  
                  if (isDashboardPage) {
                    return (
                      <li key={link.name}>
                        <PrefetchLink
                          href={link.href}
                          className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                        >
                          {link.name}
                        </PrefetchLink>
                      </li>
                    );
                  }
                  
                  return (
                    <li key={link.name}>
                      <a
                        href={link.href}
                        className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {link.name}
                      </a>
                    </li>
                  );
                })}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © 2024 OptiRoute Pro. All rights reserved.
          </p>
          <p className="text-sm text-muted-foreground flex items-center mt-4 md:mt-0">
            Made with <Heart className="h-4 w-4 mx-1 text-red-500" /> for efficient logistics
          </p>
        </div>
      </div>
    </footer>
  );
} 