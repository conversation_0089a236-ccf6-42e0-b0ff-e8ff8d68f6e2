"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Crown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  Users,
  Route,
  Camera,
  BarChart3,
} from "lucide-react";
import { useSubscriptionLimits } from "@/hooks/useSubscriptionLimits";
import { SubscriptionLimitDialog } from "./SubscriptionLimitDialog";
import { PlanTier, type LimitCheckResult } from "@/lib/subscription-limits";

export function SubscriptionTestPanel() {
  const subscriptionLimits = useSubscriptionLimits();
  const [testStops, setTestStops] = useState(5);
  const [testImages, setTestImages] = useState(3);
  const [limitDialog, setLimitDialog] = useState<{
    isOpen: boolean;
    limitResult: LimitCheckResult | null;
    feature?: string;
  }>({
    isOpen: false,
    limitResult: null,
  });

  const testRouteOptimization = () => {
    const result = subscriptionLimits.checkRouteOptimization(testStops);
    if (!result.allowed) {
      setLimitDialog({
        isOpen: true,
        limitResult: result,
        feature: "Route Optimization Test",
      });
    } else {
      alert("✅ Route optimization allowed!");
    }
  };

  const testImageUpload = () => {
    const result = subscriptionLimits.checkImageUpload(testImages);
    if (!result.allowed) {
      setLimitDialog({
        isOpen: true,
        limitResult: result,
        feature: "Image Upload Test",
      });
    } else {
      alert("✅ Image upload allowed!");
    }
  };

  const testFeatureAccess = (feature: 'bulkOptimization' | 'exportRoutes' | 'advancedAnalytics') => {
    const result = subscriptionLimits.checkFeatureAccess(feature);
    if (!result.allowed) {
      setLimitDialog({
        isOpen: true,
        limitResult: result,
        feature: feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
      });
    } else {
      alert(`✅ ${feature} access allowed!`);
    }
  };

  const getPlanBadgeColor = (tier: PlanTier) => {
    switch (tier) {
      case PlanTier.TRIAL:
        return "orange";
      case PlanTier.PRO:
        return "blue";
      case PlanTier.UNLIMITED:
        return "purple";
      default:
        return "gray";
    }
  };

  const formatLimit = (limit: number) => {
    return limit === Infinity ? '∞' : limit.toString();
  };

  if (subscriptionLimits.isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading subscription data...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Plan Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5" />
                Subscription Testing Panel
              </CardTitle>
              <CardDescription>
                Test subscription limits and features for different plan tiers
              </CardDescription>
            </div>
            <Badge color={getPlanBadgeColor(subscriptionLimits.planTier)} variant="outline">
              {subscriptionLimits.planTier} Plan
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Usage */}
          {subscriptionLimits.usage && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Routes This Month</span>
                  <span className="font-medium">
                    {subscriptionLimits.usage.routesOptimizedThisMonth} / {formatLimit(subscriptionLimits.planLimits.monthlyRouteLimit)}
                  </span>
                </div>
                <Progress 
                  value={subscriptionLimits.getUsagePercentage(
                    subscriptionLimits.usage.routesOptimizedThisMonth,
                    subscriptionLimits.planLimits.monthlyRouteLimit
                  )} 
                  className="h-2" 
                />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Total Routes</span>
                  <span className="font-medium">
                    {subscriptionLimits.usage.totalRoutes} / {formatLimit(subscriptionLimits.planLimits.maxSavedRoutes)}
                  </span>
                </div>
                <Progress 
                  value={subscriptionLimits.getUsagePercentage(
                    subscriptionLimits.usage.totalRoutes,
                    subscriptionLimits.planLimits.maxSavedRoutes
                  )} 
                  className="h-2" 
                />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Active Routes</span>
                  <span className="font-medium">{subscriptionLimits.usage.activeRoutes}</span>
                </div>
              </div>
            </div>
          )}

          <Separator />

          {/* Plan Limits */}
          <div>
            <h4 className="font-medium mb-3">Plan Limits</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Route className="h-4 w-4 text-muted-foreground" />
                <span>Max Stops: {formatLimit(subscriptionLimits.planLimits.maxStopsPerRoute)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Camera className="h-4 w-4 text-muted-foreground" />
                <span>Max Images: {subscriptionLimits.planLimits.maxImagesPerUpload}</span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span>Monthly Routes: {formatLimit(subscriptionLimits.planLimits.monthlyRouteLimit)}</span>
              </div>
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                <span>Saved Routes: {formatLimit(subscriptionLimits.planLimits.maxSavedRoutes)}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Feature Access */}
          <div>
            <h4 className="font-medium mb-3">Feature Access</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <span className="text-sm">Bulk Optimization</span>
                {subscriptionLimits.planLimits.canUseBulkOptimization ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <span className="text-sm">Export Routes</span>
                {subscriptionLimits.planLimits.canExportRoutes ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <span className="text-sm">Advanced Analytics</span>
                {subscriptionLimits.planLimits.canUseAdvancedAnalytics ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Testing Controls */}
          <div>
            <h4 className="font-medium mb-3">Test Limits</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Route Optimization Test */}
              <div className="space-y-3">
                <Label>Test Route Optimization</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={testStops}
                    onChange={(e) => setTestStops(parseInt(e.target.value) || 0)}
                    min="1"
                    max="100"
                    className="w-20"
                  />
                  <span className="text-sm text-muted-foreground">stops</span>
                  <Button size="sm" onClick={testRouteOptimization}>
                    Test
                  </Button>
                </div>
              </div>

              {/* Image Upload Test */}
              <div className="space-y-3">
                <Label>Test Image Upload</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={testImages}
                    onChange={(e) => setTestImages(parseInt(e.target.value) || 0)}
                    min="1"
                    max="50"
                    className="w-20"
                  />
                  <span className="text-sm text-muted-foreground">images</span>
                  <Button size="sm" onClick={testImageUpload}>
                    Test
                  </Button>
                </div>
              </div>
            </div>

            {/* Feature Access Tests */}
            <div className="mt-4 space-y-2">
              <Label>Test Feature Access</Label>
              <div className="flex flex-wrap gap-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => testFeatureAccess('bulkOptimization')}
                >
                  Bulk Optimization
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => testFeatureAccess('exportRoutes')}
                >
                  Export Routes
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => testFeatureAccess('advancedAnalytics')}
                >
                  Advanced Analytics
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subscription Limit Dialog */}
      {limitDialog.limitResult && (
        <SubscriptionLimitDialog
          isOpen={limitDialog.isOpen}
          onClose={() => setLimitDialog({ isOpen: false, limitResult: null })}
          limitResult={limitDialog.limitResult}
          planTier={subscriptionLimits.planTier}
          feature={limitDialog.feature}
        />
      )}
    </div>
  );
} 