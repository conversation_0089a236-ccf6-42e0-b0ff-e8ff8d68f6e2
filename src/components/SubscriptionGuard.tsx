"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useUserBillingStatus } from "@/hooks/useUserBillingStatus";
import { authClient } from "@/server/auth/client";
import { Spinner } from "@/components/Spinner";
import { clientEnv } from "@/env/client";

interface SubscriptionGuardProps {
  children: React.ReactNode;
}

export function SubscriptionGuard({ children }: SubscriptionGuardProps) {
  const { data: session, isPending: sessionLoading } = authClient.useSession();
  const { billingState, isLoading: billingLoading, error: billingError } = useUserBillingStatus({ 
    enabled: !!session 
  });
  const router = useRouter();

  useEffect(() => {
    // Don't check if still loading session
    if (sessionLoading || !session) {
      return;
    }

    // If Polar is disabled, allow access (for development)
    if (!clientEnv.NEXT_PUBLIC_ENABLE_POLAR) {
      return;
    }

    // If there's a billing error, log it but don't block access (for development)
    if (billingError) {
      console.warn("Billing state error (allowing access for development):", billingError);
      return;
    }

    // Don't check if still loading billing data
    if (billingLoading) {
      return;
    }

    // Check if user has any active subscription or trial
    const hasActiveSubscription = billingState?.activeSubscriptions?.some(sub => 
      sub.status === 'active' || sub.status === 'trialing'
    );

    // If no active subscription, redirect to home page with pricing section
    if (!hasActiveSubscription) {
      router.push('/#pricing');
    }
  }, [session, sessionLoading, billingState, billingLoading, billingError, router]);

  // Show loading while checking session
  if (sessionLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spinner />
      </div>
    );
  }

  // If Polar is disabled, always allow access
  if (!clientEnv.NEXT_PUBLIC_ENABLE_POLAR) {
    return <>{children}</>;
  }

  // If there's a billing error, allow access but log the error
  if (billingError) {
    console.warn("Subscription guard: Billing error detected, allowing access for development:", billingError);
    return <>{children}</>;
  }

  // Show loading while checking billing data
  if (billingLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spinner />
      </div>
    );
  }

  // Check subscription status
  const hasActiveSubscription = billingState?.activeSubscriptions?.some(sub => 
    sub.status === 'active' || sub.status === 'trialing'
  );

  if (!hasActiveSubscription) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spinner />
      </div>
    );
  }

  return <>{children}</>;
} 