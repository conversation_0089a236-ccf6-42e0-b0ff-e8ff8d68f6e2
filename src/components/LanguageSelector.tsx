'use client';

import { useLanguage } from '@/context/LanguageContext';
import { Languages } from 'lucide-react';
import { type Language } from '@/lib/i18n';

interface LanguageSelectorProps {
  variant?: 'footer' | 'header' | 'compact';
  className?: string;
}

export function LanguageSelector({ variant = 'footer', className = '' }: LanguageSelectorProps) {
  const { language, setLanguage, t } = useLanguage();

  const languages: Array<{ code: Language; name: string; flag: string }> = [
    { code: 'en', name: t.settings.english, flag: '🇺🇸' },
    { code: 'ru', name: t.settings.russian, flag: '🇷🇺' },
  ];

  const currentLanguage = languages.find(lang => lang.code === language) || languages[0];

  if (variant === 'compact') {
    return (
      <div className={`relative ${className}`}>
        <select
          value={language}
          onChange={(e) => setLanguage(e.target.value as Language)}
          className="appearance-none bg-transparent text-sm font-medium cursor-pointer focus:outline-none"
        >
          {languages.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {lang.flag} {lang.name}
            </option>
          ))}
        </select>
      </div>
    );
  }

  if (variant === 'header') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => {
            const nextLanguage = language === 'en' ? 'ru' : 'en';
            setLanguage(nextLanguage);
          }}
          className="flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        >
          <Languages className="w-4 h-4" />
          <span>{currentLanguage?.flag}</span>
          <span className="hidden sm:inline">{currentLanguage?.name}</span>
        </button>
      </div>
    );
  }

  // Footer variant (default)
  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <Languages className="w-4 h-4 text-gray-500" />
      <div className="flex gap-2">
        {languages.map((lang) => (
          <button
            key={lang.code}
            onClick={() => setLanguage(lang.code)}
            className={`flex items-center gap-1 px-2 py-1 text-sm rounded transition-colors ${
              language === lang.code
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
            }`}
          >
            <span>{lang.flag}</span>
            <span>{lang.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
} 