'use client';

import { useEffect, useRef, useState } from 'react';
import { useTheme } from 'next-themes';
import * as maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { RouteStop } from '@/lib/route-optimization';

interface RouteMapProps {
  stops: RouteStop[];
  polyline?: string;
  sections?: any[]; // Route sections with individual polylines
}

export function RouteMap({ stops, polyline: encodedPolyline, sections }: RouteMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<maplibregl.Map | null>(null);
  const { resolvedTheme } = useTheme();
  const [mapLoaded, setMapLoaded] = useState(false);
  const markersRef = useRef<maplibregl.Marker[]>([]);

  // Decode HERE flexible polyline or standard polyline
  const decodePolyline = (encoded: string): [number, number][] => {
    if (!encoded || typeof encoded !== 'string' || encoded.length === 0) {
      return [];
    }

    try {
      // Try to decode as Google/Mapbox polyline format first (most common)
      return decodeGooglePolyline(encoded);
    } catch (error) {
      console.warn('Failed to decode as Google polyline, trying HERE format:', error);
      try {
        // Fallback to HERE flexible polyline format
        return decodeHEREFlexiblePolyline(encoded);
      } catch (hereError) {
        console.error('Failed to decode polyline in both formats:', hereError);
        // Ultimate fallback: direct lines between stops
        return stops.filter(stop => stop.latitude && stop.longitude)
          .map(stop => [stop.longitude!, stop.latitude!]);
      }
    }
  };

  // Standard Google/Mapbox polyline decoder
  const decodeGooglePolyline = (encoded: string): [number, number][] => {
    const coordinates: [number, number][] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let shift = 0;
      let result = 0;
      let byte: number;

      // Decode latitude
      do {
        byte = encoded.charCodeAt(index++) - 63;
        result |= (byte & 0x1f) << shift;
        shift += 5;
      } while (byte >= 0x20);

      const deltaLat = ((result & 1) ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;

      // Decode longitude
      do {
        byte = encoded.charCodeAt(index++) - 63;
        result |= (byte & 0x1f) << shift;
        shift += 5;
      } while (byte >= 0x20);

      const deltaLng = ((result & 1) ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      // Convert to degrees and push [lng, lat] for MapLibre
      coordinates.push([lng / 1e5, lat / 1e5]);
    }

    return coordinates;
  };

  // HERE flexible polyline decoder
  const decodeHEREFlexiblePolyline = (encoded: string): [number, number][] => {
    if (!encoded || encoded.length === 0) return [];

    try {
      const decodeChar = (char: string): number => {
        const charCode = char.charCodeAt(0);
        if (charCode >= 65 && charCode <= 90) return charCode - 65;
        if (charCode >= 97 && charCode <= 122) return charCode - 71;
        if (charCode >= 48 && charCode <= 57) return charCode + 4;
        if (charCode === 45) return 62;
        if (charCode === 95) return 63;
        return 0;
      };

      const decodeUnsignedInt = (encoded: string, index: number): { value: number, shift: number } => {
        let result = 0;
        let shift = 0;
        let value: number;

        do {
          if (index >= encoded.length) break;
          value = decodeChar(encoded.charAt(index++));
          const hasNext = (value & 0x20) > 0;
          value &= 0x1F;
          result |= value << shift;
          shift += 5;
          if (!hasNext) break;
        } while (true);

        return { value: result, shift: index };
      };

      const toSigned = (value: number): number => {
        return (value & 1) ? ~(value >> 1) : (value >> 1);
      };

      // Skip version
      let index = 1;
      
      // Get precision
      const headerByte = decodeChar(encoded.charAt(index++));
      const precision = headerByte & 0x0F;
      const multiplier = Math.pow(10, precision);

      const coordinates: [number, number][] = [];
      let lastLat = 0;
      let lastLng = 0;

      while (index < encoded.length) {
        const latResult = decodeUnsignedInt(encoded, index);
        index = latResult.shift;

        if (index >= encoded.length) break;

        const lngResult = decodeUnsignedInt(encoded, index);
        index = lngResult.shift;

        lastLat += toSigned(latResult.value);
        lastLng += toSigned(lngResult.value);

        coordinates.push([lastLng / multiplier, lastLat / multiplier]);
      }

      return coordinates;
    } catch (e) {
      throw new Error('Failed to decode HERE flexible polyline');
    }
  };

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current) return;

    map.current = new maplibregl.Map({
      container: mapContainer.current,
      style: resolvedTheme === 'dark'
        ? 'https://basemaps.cartocdn.com/gl/dark-matter-gl-style/style.json'
        : 'https://basemaps.cartocdn.com/gl/positron-gl-style/style.json',
      center: [-95.7129, 37.0902],
      zoom: 3,
      attributionControl: false
    });

    // Add navigation controls
    map.current.addControl(new maplibregl.NavigationControl({
      showCompass: false,
      showZoom: true
    }), 'top-right');

    map.current.on('load', () => {
      setMapLoaded(true);
    });

    return () => {
      markersRef.current.forEach(marker => marker.remove());
      markersRef.current = [];
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, [resolvedTheme]);

  // Add markers
  useEffect(() => {
    if (!mapLoaded || !map.current || !stops?.length) return;

    // Clear previous markers
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current = [];

    const bounds = new maplibregl.LngLatBounds();
    let hasValidBounds = false;

    // Filter stops with valid coordinates
    const validStops = stops.filter(stop => 
      typeof stop.latitude === 'number' && 
      typeof stop.longitude === 'number' &&
      Math.abs(stop.latitude) <= 90 && 
      Math.abs(stop.longitude) <= 180 &&
      !isNaN(stop.latitude) &&
      !isNaN(stop.longitude)
    );

    console.log('Adding markers for stops:', validStops.map(stop => ({
      address: stop.address,
      lat: stop.latitude,
      lng: stop.longitude
    })));

    validStops.forEach((stop, index) => {
      if (!stop.latitude || !stop.longitude) return;

      const lat = Number(stop.latitude);
      const lng = Number(stop.longitude);

      // Validate coordinates again
      if (isNaN(lat) || isNaN(lng) || Math.abs(lat) > 90 || Math.abs(lng) > 180) {
        console.warn(`Invalid coordinates for stop ${index}:`, { lat, lng, address: stop.address });
        return;
      }

      bounds.extend([lng, lat]);
      hasValidBounds = true;

      // Create marker element with proper positioning
      const el = document.createElement('div');
      el.className = 'route-marker-element';
      
      // Apply styles with proper positioning
      Object.assign(el.style, {
        width: '32px',
        height: '32px',
        borderRadius: '50%',
        backgroundColor: resolvedTheme === 'dark' ? '#3b82f6' : '#2563eb',
        color: 'white',
        fontWeight: 'bold',
        fontSize: '14px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        border: `3px solid ${resolvedTheme === 'dark' ? '#1f2937' : 'white'}`,
        boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
        cursor: 'pointer',
        // Ensure stable positioning
        position: 'absolute',
        transform: 'translate(-50%, -50%)', // Center the marker properly
        transformOrigin: 'center center',
        // Prevent subpixel rendering issues
        backfaceVisibility: 'hidden',
        willChange: 'transform',
        // Disable interactions that might interfere
        userSelect: 'none',
        pointerEvents: 'auto',
        // Ensure proper z-index
        zIndex: '1000'
      });

      // Special styling for start, end, and completed stops
      if (index === 0) {
        el.style.backgroundColor = resolvedTheme === 'dark' ? '#10b981' : '#059669';
      } else if (index === validStops.length - 1) {
        el.style.backgroundColor = resolvedTheme === 'dark' ? '#ef4444' : '#dc2626';
      } else if (stop.isCompleted) {
        el.style.backgroundColor = resolvedTheme === 'dark' ? '#6b7280' : '#4b5563';
      }

      el.textContent = String(index + 1);

      // Hover effects with proper transform
      const originalTransform = 'translate(-50%, -50%) scale(1)';
      const hoverTransform = 'translate(-50%, -50%) scale(1.1)';
      
      el.addEventListener('mouseenter', () => {
        el.style.transform = hoverTransform;
        el.style.zIndex = '1001';
      });
      
      el.addEventListener('mouseleave', () => {
        el.style.transform = originalTransform;
        el.style.zIndex = '1000';
      });

      // Create popup
      const popupContent = `
        <div style="padding: 8px 12px; font-family: system-ui, sans-serif;">
          <div style="font-weight: 600; margin-bottom: 4px; color: ${resolvedTheme === 'dark' ? '#f9fafb' : '#111827'};">
            ${index === 0 ? '🚀 Start' : index === validStops.length - 1 ? '🏁 End' : `📍 Stop ${index}`}
          </div>
          <div style="font-size: 12px; color: ${resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'}; line-height: 1.4;">
            ${stop.address}
          </div>
          ${stop.isCompleted ? `
            <div style="font-size: 11px; color: #10b981; margin-top: 4px; font-weight: 500;">
              ✓ Completed
            </div>
          ` : ''}
        </div>
      `;

      const popup = new maplibregl.Popup({ 
        offset: 35,
        closeButton: false,
        closeOnClick: false,
        className: 'route-popup'
      }).setHTML(popupContent);

      el.addEventListener('click', () => {
        popup.addTo(map.current!);
        setTimeout(() => popup.remove(), 3000);
      });

      // Create and add marker with proper anchoring
      const marker = new maplibregl.Marker({
        element: el,
        anchor: 'center', // This should center the marker properly
        offset: [0, 0] // No additional offset needed
      })
        .setLngLat([lng, lat]); // Use validated coordinates

      if (map.current) {
        marker.addTo(map.current);
        console.log(`Added marker ${index + 1} at [${lng}, ${lat}] for address: ${stop.address}`);
      }

      markersRef.current.push(marker);
    });

    // Fit map to bounds
    if (hasValidBounds && map.current) {
      if (validStops.length === 1) {
        const stop = validStops[0];
        if (stop && stop.longitude && stop.latitude) {
          map.current.setCenter([Number(stop.longitude), Number(stop.latitude)]);
          map.current.setZoom(14);
        }
      } else {
        map.current.fitBounds(bounds, {
          padding: 50,
          maxZoom: 13
        });
      }
    }
  }, [mapLoaded, stops, resolvedTheme]);

  // Add polylines for routes
  useEffect(() => {
    if (!mapLoaded || !map.current) return;

    // Remove previous route layers
    if (map.current.getLayer('route-line')) {
      map.current.removeLayer('route-line');
    }
    if (map.current.getSource('route')) {
      map.current.removeSource('route');
    }

    // If we have sections, use them instead of the single polyline
    if (sections && sections.length > 0) {
      try {
        // Create an array to hold all route features
        const routeFeatures: GeoJSON.Feature[] = [];
        const bounds = new maplibregl.LngLatBounds();
        let hasValidBounds = false;

        // Process each section individually
        sections.forEach((section, idx) => {
          if (!section.polyline) {
            return;
          }

          try {
            // Use our custom decoder for HERE flexible polyline format
            const coordinates = decodeHEREFlexiblePolyline(section.polyline);

            if (coordinates.length > 0) {
              // Validate coordinates
              const isValidPath = coordinates.every(coord =>
                Math.abs(coord[0]) <= 180 && Math.abs(coord[1]) <= 90
              );

              if (isValidPath) {
                // Extend bounds
                coordinates.forEach(coord => {
                  bounds.extend(coord as maplibregl.LngLatLike);
                  hasValidBounds = true;
                });

                // Add feature
                routeFeatures.push({
                  type: 'Feature',
                  properties: {},
                  geometry: {
                    type: 'LineString',
                    coordinates: coordinates
                  }
                });
              }
            } else {
              // If decoding failed, fall back to direct line between waypoints
              if (idx < stops.length - 1) {
                const start = stops[idx];
                const end = stops[idx + 1];

                if (start && end && start.longitude && start.latitude && end.longitude && end.latitude) {
                  const directLine = [
                    [start.longitude, start.latitude],
                    [end.longitude, end.latitude]
                  ];

                  routeFeatures.push({
                    type: 'Feature',
                    properties: {},
                    geometry: {
                      type: 'LineString',
                      coordinates: directLine
                    }
                  });

                  bounds.extend([start.longitude, start.latitude]);
                  bounds.extend([end.longitude, end.latitude]);
                  hasValidBounds = true;
                }
              }
            }
          } catch (e) {
            console.warn('Error decoding polyline for section:', e);
          }
        });

        // Add route to map if we have features
        if (routeFeatures.length > 0) {
          map.current.addSource('route', {
            type: 'geojson',
            data: {
              type: 'FeatureCollection',
              features: routeFeatures
            }
          });

          map.current.addLayer({
            id: 'route-line',
            type: 'line',
            source: 'route',
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': resolvedTheme === 'dark' ? '#3b82f6' : '#2563eb',
              'line-width': 4,
              'line-opacity': 0.8
            }
          });

          // Fit map to bounds
          if (hasValidBounds && !bounds.isEmpty()) {
            map.current.fitBounds(bounds, {
              padding: 50,
              maxZoom: 13
            });
          }
        }
      } catch (error) {
        console.error('Error adding route sections:', error);
      }
      return;
    }

    if (!encodedPolyline) {
      // No polyline provided, create direct lines between stops
      const validStops = stops.filter(stop => stop.latitude && stop.longitude);
      if (validStops.length > 1) {
        const coordinates = validStops.map(stop => [stop.longitude!, stop.latitude!]);
        
        map.current.addSource('route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'LineString',
              coordinates: coordinates
            }
          }
        });

        map.current.addLayer({
          id: 'route-line',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': resolvedTheme === 'dark' ? '#3b82f6' : '#2563eb',
            'line-width': 4,
            'line-opacity': 0.8
          }
        });
      }
      return;
    }

    try {
      // Decode the polyline
      const coordinates = decodePolyline(encodedPolyline);
      
      console.log('Polyline decoding result:', {
        originalLength: encodedPolyline.length,
        decodedCoordinates: coordinates.length,
        firstFewCoords: coordinates.slice(0, 3),
        lastFewCoords: coordinates.slice(-3)
      });
      
      if (coordinates.length > 0) {
        // Validate coordinates
        const isValidPath = coordinates.every(coord =>
          Math.abs(coord[0]) <= 180 && Math.abs(coord[1]) <= 90
        );

        if (isValidPath) {
          map.current.addSource('route', {
            type: 'geojson',
            data: {
              type: 'Feature',
              properties: {},
              geometry: {
                type: 'LineString',
                coordinates: coordinates
              }
            }
          });

          map.current.addLayer({
            id: 'route-line',
            type: 'line',
            source: 'route',
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': resolvedTheme === 'dark' ? '#3b82f6' : '#2563eb',
              'line-width': 4,
              'line-opacity': 0.8
            }
          });

          // Fit map to polyline bounds
          const bounds = new maplibregl.LngLatBounds();
          coordinates.forEach(coord => bounds.extend(coord as maplibregl.LngLatLike));
          
          if (!bounds.isEmpty()) {
            map.current.fitBounds(bounds, {
              padding: 50,
              maxZoom: 13
            });
          }
        } else {
          console.warn('Invalid coordinates in polyline, falling back to direct lines');
          // Fallback handled above when no polyline
        }
      }
    } catch (error) {
      console.error('Failed to process polyline:', error);
      // Fallback to direct lines between stops handled above
    }
  }, [mapLoaded, encodedPolyline, sections, stops, resolvedTheme]);

  return (
    <div
      ref={mapContainer}
      className="w-full h-full min-h-[400px] rounded-xl overflow-hidden border border-border shadow-sm"
      style={{
        position: 'relative',
        zIndex: 10
      }}
    />
  );
} 