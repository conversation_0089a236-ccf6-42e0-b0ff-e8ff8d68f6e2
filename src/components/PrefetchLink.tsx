"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useKitzeUI } from "@/components/KitzeUIContext";
import { ReactNode, useCallback, useRef } from "react";

interface PrefetchLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  prefetchOnMount?: boolean; // For mobile menu items
  [key: string]: any; // Allow other props to pass through
}

export function PrefetchLink({ 
  href, 
  children, 
  className, 
  prefetchOnMount = false,
  ...props 
}: PrefetchLinkProps) {
  const router = useRouter();
  const { isMobile } = useKitzeUI();
  const prefetchedRef = useRef(false);

  const prefetchPage = useCallback(() => {
    if (!prefetchedRef.current) {
      router.prefetch(href);
      prefetchedRef.current = true;
    }
  }, [router, href]);

  // Prefetch immediately on mount for mobile menu items
  if (prefetchOnMount && !prefetchedRef.current) {
    prefetchPage();
  }

  const handleMouseEnter = useCallback(() => {
    // Only prefetch on hover for desktop
    if (!isMobile) {
      prefetchPage();
    }
  }, [isMobile, prefetchPage]);

  return (
    <Link
      href={href}
      className={className}
      onMouseEnter={handleMouseEnter}
      {...props}
    >
      {children}
    </Link>
  );
} 