"use client";

import { useEffect } from "react";
import { usePrefetchStrategy } from "@/hooks/usePrefetchStrategy";
import { usePathname } from "next/navigation";

export function PrefetchManager() {
  const { prefetchCriticalPages, prefetchPages } = usePrefetchStrategy();
  const pathname = usePathname();

  useEffect(() => {
    // Prefetch critical dashboard pages after initial load
    const timer = setTimeout(() => {
      prefetchCriticalPages();
    }, 500); // Wait 500ms after initial load

    return () => clearTimeout(timer);
  }, [prefetchCriticalPages]);

  useEffect(() => {
    // Prefetch related pages based on current page
    const relatedPages: Record<string, string[]> = {
      "/routes": ["/analytics", "/settings"],
      "/analytics": ["/routes", "/settings"],
      "/settings": ["/routes", "/analytics"],
    };

    const currentPageRelated = relatedPages[pathname];
    if (currentPageRelated) {
      // Prefetch related pages with a delay
      const timer = setTimeout(() => {
        prefetchPages(currentPageRelated, { delay: 1000 });
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [pathname, prefetchPages]);

  // This component doesn't render anything
  return null;
} 