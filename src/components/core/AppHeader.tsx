"use client";

import { AppHeaderUser } from "./HeaderUser";
import { blogLink, homeLink } from "@/config/links";
import { useKitzeUI } from "@/components/KitzeUIContext";
import { ThemeSwitchMinimalNextThemes } from "@/components/ThemeSwitchMinimalNextThemes";
import { HeaderCustomized } from "@/components/core/HeaderCustomized";
import Link from "next/link";
import { PrefetchLink } from "@/components/PrefetchLink";
import { MapPin, Route, BarChart3, Settings, Menu, User } from "lucide-react";
import { APP_NAME } from "@/config/config";
import { motion } from "framer-motion";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { SimpleDropdownMenu } from "@/components/SimpleDropdownMenu";
import { CommonMenuItem } from "@/components/CommonMenuItem";
import { CommonMenuSeparator } from "@/components/CommonMenuSeparator";
import { HeaderUserDropdownMenu } from "./HeaderUserDropdownMenu";
import { filterEnabledLinks } from "@/lib/linkUtils";

// Dashboard navigation links
const dashboardLinks = [
  {
    href: "/routes",
    label: "Routes",
    icon: Route,
  },
  {
    href: "/settings",
    label: "Settings",
    icon: Settings,
  },
  {
    href: "/analytics",
    label: "Analytics", 
    icon: BarChart3,
  },
];

export default function AppHeader() {
  const { isMobile } = useKitzeUI();
  const pathname = usePathname();

  // Links for user dropdown menu
  const userLinks = [homeLink, blogLink];
  const enabledUserLinks = filterEnabledLinks(userLinks);

  // Mobile navigation dropdown content
  const MobileNavigation = () => (
    <div className="py-2 w-48">
      {/* Dashboard Navigation */}
      {dashboardLinks.map((link) => {
        const Icon = link.icon;
        const isActive = pathname === link.href || 
          (pathname.startsWith(link.href) && link.href !== "/routes") ||
          (pathname === "/app" && link.href === "/routes"); // Redirect /app to /routes
        
        return (
          <CommonMenuItem
            key={link.href}
            href={link.href}
            leftIcon={Icon}
            className={cn(
              isActive && "bg-accent text-accent-foreground"
            )}
          >
            {link.label}
          </CommonMenuItem>
        );
      })}
      
      <CommonMenuSeparator />
      
      {/* Account Menu Item - Custom Implementation */}
      <SimpleDropdownMenu
        content={<HeaderUserDropdownMenu links={enabledUserLinks} />}
        mobileView="keep"
        align="end"
      >
        <div className="flex items-center gap-3 rounded-sm p-2 text-sm transition-all outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>Account</span>
        </div>
      </SimpleDropdownMenu>
    </div>
  );

  return (
    <HeaderCustomized
      classNames={{
        root: "relative border-b border-border/40 bg-background/80 backdrop-blur-xl",
      }}
      leftSide={
        <div className="horizontal gap-6">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <MapPin className="h-6 w-6 text-foreground" />
            <span className="text-xl font-bold text-foreground">{APP_NAME}</span>
          </Link>
        </div>
      }
      middle={
        /* Dashboard Navigation - Desktop - Centered */
        !isMobile ? (
          <nav className="flex items-center justify-center space-x-8">
            {dashboardLinks.map((link, index) => {
              const Icon = link.icon;
              const isActive = pathname === link.href || 
                (pathname.startsWith(link.href) && link.href !== "/routes") ||
                (pathname === "/app" && link.href === "/routes"); // Redirect /app to /routes
              
              return (
                <motion.div
                  key={link.href}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <PrefetchLink
                    href={link.href}
                    className={cn(
                      "flex items-center space-x-2 text-sm font-medium transition-colors duration-200 hover:text-foreground px-3 py-2 rounded-md",
                      isActive 
                        ? "text-foreground bg-accent" 
                        : "text-muted-foreground hover:bg-accent/50"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{link.label}</span>
                  </PrefetchLink>
                </motion.div>
              );
            })}
          </nav>
        ) : undefined
      }
      renderRightSide={() => (
        <div className="horizontal gap-4">
          <ThemeSwitchMinimalNextThemes />
          
          {/* Mobile Navigation Menu */}
          {isMobile && (
            <SimpleDropdownMenu
              content={<MobileNavigation />}
              mobileView="keep"
              align="end"
              side="bottom"
            >
              <Menu className="h-5 w-5" />
            </SimpleDropdownMenu>
          )}
          
          {/* Desktop User Menu */}
          {!isMobile && <AppHeaderUser links={enabledUserLinks} />}
        </div>
      )}
    />
  );
}
