"use client";

import { useCurrentUser } from "@/hooks/useCurrentUser";
import { Button } from "@/components/ui/enhanced-button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  HeaderUserDropdownMenu,
  type LinkType,
} from "./HeaderUserDropdownMenu";
import { authClient } from "@/server/auth/client";
import { useUserBillingStatus } from "@/hooks/useUserBillingStatus";
import { Crown, LucideLogIn, LucideMenu } from "lucide-react";
import { SimpleDropdownMenu } from "@/components/SimpleDropdownMenu";
import { CustomBadge } from "@/components/CustomBadge";
import { AnimatePresence, motion } from "framer-motion";
import { CommonMenuItem } from "@/components/CommonMenuItem";
import { useKitzeUI } from "@/components/KitzeUIContext";
import { filterEnabledLinks } from "@/lib/linkUtils";
import { useIsImpersonating } from "@/hooks/useIsImpersonating";
import { SimpleTooltip } from "@/components/SimpleTooltip";
import { CommonMenuSeparator } from "@/components/CommonMenuSeparator";
import { clientEnv } from "@/env/client";

interface AppHeaderUserProps {
  links: (LinkType | null | undefined)[] | undefined;
}

export function AppHeaderUser({ links }: AppHeaderUserProps) {
  const realUser = useCurrentUser();
  const { data: userSession, isPending } = authClient.useSession();
  const { isPro } = useUserBillingStatus({ 
    enabled: !!userSession && clientEnv.NEXT_PUBLIC_ENABLE_POLAR 
  });
  const { isMobile } = useKitzeUI();
  const { isImpersonating, impersonatedUserName } = useIsImpersonating();

  const isLoading = isPending;
  const isLoggedOut = !isLoading && !userSession;
  const isLoggedIn = !isLoading && !!userSession;

  const enabledLinks = filterEnabledLinks(links);

  const motionProps = {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
    transition: { duration: 0.2 },
  };

  return (
    <AnimatePresence mode="wait">
      {isLoading && (
        <motion.div key="loading" {...motionProps}>
          <Avatar>
            <AvatarFallback className="animate-pulse cursor-wait bg-gray-300 select-none dark:bg-gray-700">
              U
            </AvatarFallback>
          </Avatar>
        </motion.div>
      )}

      {isLoggedOut && (
        <motion.div
          className="flex items-center gap-2"
          key="login"
          {...motionProps}
        >
          {!isMobile && (
            <Button leftIcon={LucideLogIn} href="/signin">
              Login
            </Button>
          )}
          {isMobile && (
            <SimpleDropdownMenu
              mobileView="bottom-drawer"
              content={
                <>
                  {enabledLinks.map((link) => (
                    <CommonMenuItem
                      key={link.href}
                      href={link.href}
                      leftIcon={link.icon}
                    >
                      {link.label}
                    </CommonMenuItem>
                  ))}
                  <CommonMenuSeparator />
                  <CommonMenuItem leftIcon={LucideLogIn} href="/signin">
                    Login
                  </CommonMenuItem>
                </>
              }
            >
              <Button
                variant="ghost"
                size="icon"
                leftIcon={LucideMenu}
              />
            </SimpleDropdownMenu>
          )}
        </motion.div>
      )}

      {isLoggedIn && (
        <motion.div
          className="flex items-center gap-2"
          key="user"
          {...motionProps}
        >
          {!isMobile && (
            <div className="flex items-center gap-2">
              {isPro && (
                <SimpleTooltip content="Pro User">
                  <CustomBadge color="bg-violet-500" size="sm" leftIcon={Crown}>
                    Pro
                  </CustomBadge>
                </SimpleTooltip>
              )}
              {isImpersonating && (
                <SimpleTooltip
                  content={`Impersonating ${impersonatedUserName}`}
                >
                  <div className="h-2 w-2 rounded-full bg-yellow-500" />
                </SimpleTooltip>
              )}
            </div>
          )}

          <SimpleDropdownMenu
            mobileView="bottom-drawer"
            content={<HeaderUserDropdownMenu links={enabledLinks} />}
          >
            <Avatar className="cursor-pointer">
              <AvatarImage src={realUser?.profilePic ?? undefined} />
              <AvatarFallback className="select-none">
                {realUser?.name?.charAt(0)?.toUpperCase() ?? "U"}
              </AvatarFallback>
            </Avatar>
          </SimpleDropdownMenu>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
