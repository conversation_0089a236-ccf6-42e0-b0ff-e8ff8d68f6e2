"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useKitzeUI } from "@/components/KitzeUIContext";
import { ReactNode, useCallback, useRef, useEffect } from "react";

interface SmartPrefetchLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  prefetchOnMount?: boolean;
  prefetchOnVisible?: boolean; // Prefetch when link becomes visible
  prefetchOnHover?: boolean; // Prefetch on hover (default: true for desktop)
  [key: string]: any;
}

export function SmartPrefetchLink({ 
  href, 
  children, 
  className, 
  prefetchOnMount = false,
  prefetchOnVisible = false,
  prefetchOnHover = true,
  ...props 
}: SmartPrefetchLinkProps) {
  const router = useRouter();
  const { isMobile } = useKitzeUI();
  const prefetchedRef = useRef(false);
  const linkRef = useRef<HTMLAnchorElement>(null);

  const prefetchPage = useCallback(() => {
    if (!prefetchedRef.current) {
      router.prefetch(href);
      prefetchedRef.current = true;
    }
  }, [router, href]);

  // Prefetch immediately on mount
  useEffect(() => {
    if (prefetchOnMount) {
      prefetchPage();
    }
  }, [prefetchOnMount, prefetchPage]);

  // Intersection Observer for visible prefetching
  useEffect(() => {
    if (!prefetchOnVisible || !linkRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            prefetchPage();
            observer.disconnect(); // Stop observing once prefetched
          }
        });
      },
      {
        rootMargin: "100px", // Start prefetching 100px before the link is visible
        threshold: 0.1,
      }
    );

    observer.observe(linkRef.current);

    return () => observer.disconnect();
  }, [prefetchOnVisible, prefetchPage]);

  const handleMouseEnter = useCallback(() => {
    if (prefetchOnHover && !isMobile) {
      prefetchPage();
    }
  }, [prefetchOnHover, isMobile, prefetchPage]);

  const handleTouchStart = useCallback(() => {
    // Prefetch on touch start for mobile
    if (isMobile) {
      prefetchPage();
    }
  }, [isMobile, prefetchPage]);

  return (
    <Link
      ref={linkRef}
      href={href}
      className={className}
      onMouseEnter={handleMouseEnter}
      onTouchStart={handleTouchStart}
      {...props}
    >
      {children}
    </Link>
  );
} 