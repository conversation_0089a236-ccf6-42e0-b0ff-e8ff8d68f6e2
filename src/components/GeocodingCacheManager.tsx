'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/enhanced-button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Database, 
  Trash2, 
  BarChart3, 
  MapPin, 
  Clock, 
  HardDrive,
  RefreshCw 
} from 'lucide-react';
import { GeocodingCache } from '@/lib/geocoding-cache';

interface CacheStats {
  size: number;
  oldestEntry: string | null;
  newestEntry: string | null;
  totalSize: string;
}

export function GeocodingCacheManager() {
  const [cacheStats, setCacheStats] = useState<CacheStats>({
    size: 0,
    oldestEntry: null,
    newestEntry: null,
    totalSize: '0 KB',
  });
  const [isClearing, setIsClearing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const loadCacheStats = () => {
    try {
      const cache = GeocodingCache.getInstance();
      const stats = cache.getStats();
      setCacheStats(stats);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to load cache stats:', error);
    }
  };

  useEffect(() => {
    loadCacheStats();
  }, []);

  const handleClearCache = async () => {
    setIsClearing(true);
    try {
      const cache = GeocodingCache.getInstance();
      cache.clear();
      loadCacheStats();
    } catch (error) {
      console.error('Failed to clear cache:', error);
    } finally {
      setIsClearing(false);
    }
  };

  const handleRefreshStats = () => {
    loadCacheStats();
  };

  const formatCacheAge = () => {
    if (!cacheStats.oldestEntry || !cacheStats.newestEntry) {
      return 'No cached entries';
    }
    
    if (cacheStats.oldestEntry === cacheStats.newestEntry) {
      return `All entries from ${cacheStats.oldestEntry}`;
    }
    
    return `${cacheStats.oldestEntry} - ${cacheStats.newestEntry}`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Geocoding Cache
            </CardTitle>
            <CardDescription>
              Cached address lookups to reduce API usage and improve performance
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshStats}
            leftIcon={RefreshCw}
          >
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Cache Statistics */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Cache Statistics</h4>
            <Badge variant="outline" color="gray" className="text-xs">
              Updated {lastRefresh.toLocaleTimeString()}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <MapPin className="h-3 w-3" />
                Cached Addresses
              </div>
              <div className="text-lg font-semibold">{cacheStats.size.toLocaleString()}</div>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <HardDrive className="h-3 w-3" />
                Storage Used
              </div>
              <div className="text-lg font-semibold">{cacheStats.totalSize}</div>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                Cache Age Range
              </div>
              <div className="text-sm font-medium text-wrap">{formatCacheAge()}</div>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <BarChart3 className="h-3 w-3" />
                Cache Efficiency
              </div>
              <div className="text-lg font-semibold">
                {cacheStats.size > 0 ? (
                  <Badge color="green" variant="outline">Active</Badge>
                ) : (
                  <Badge color="gray" variant="outline">Empty</Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Cache Benefits */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Benefits</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>Reduces Mapbox API usage</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full" />
              <span>Faster address lookups</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full" />
              <span>Offline address reuse</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
              <span>7-day automatic expiration</span>
            </div>
          </div>
        </div>

        {/* Cache Management */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Cache Management</h4>
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="space-y-1">
              <div className="text-sm font-medium">Clear All Cached Data</div>
              <div className="text-xs text-muted-foreground">
                This will remove all cached addresses and force fresh API lookups
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearCache}
              loading={isClearing}
              leftIcon={Trash2}
              disabled={cacheStats.size === 0}
            >
              Clear Cache
            </Button>
          </div>
        </div>

        {/* Technical Details */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Technical Details</h4>
          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Cache Duration:</span>
              <span>7 days</span>
            </div>
            <div className="flex justify-between">
              <span>Max Entries:</span>
              <span>1,000 addresses</span>
            </div>
            <div className="flex justify-between">
              <span>Storage Method:</span>
              <span>Browser localStorage</span>
            </div>
            <div className="flex justify-between">
              <span>Auto Cleanup:</span>
              <span>Every hour</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 