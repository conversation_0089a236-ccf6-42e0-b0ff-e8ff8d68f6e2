"use client";
import { <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { type ReactNode } from "react";

interface AuthFormHeaderProps {
  title: ReactNode;
  description?: ReactNode;
}

export function AuthFormHeader({ title, description }: AuthFormHeaderProps) {
  return (
    <CardHeader className="vertical center">
      <CardTitle className="text-center text-2xl">{title}</CardTitle>
      {description && (
        <CardDescription className="text-center">{description}</CardDescription>
      )}
    </CardHeader>
  );
}
