"use client";

import { CreditCard, Check } from "lucide-react";
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface PaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
  isDefault?: boolean;
}

interface PaymentMethodsProps {
  paymentMethods: PaymentMethod[];
}

export function PaymentMethods({ paymentMethods }: PaymentMethodsProps) {
  if (!paymentMethods || paymentMethods.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Methods
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">No payment methods found.</p>
        </CardContent>
      </Card>
    );
  }

  const getCardBrandIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'amex':
        return '💳';
      default:
        return '💳';
    }
  };

  const formatCardBrand = (brand: string) => {
    return brand.charAt(0).toUpperCase() + brand.slice(1);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Payment Methods
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className="flex items-center justify-between p-3 border rounded-lg"
          >
            <div className="flex items-center gap-3">
              <span className="text-2xl">{getCardBrandIcon(method.card?.brand || '')}</span>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">
                    {formatCardBrand(method.card?.brand || '')} •••• {method.card?.last4}
                  </span>
                  {method.isDefault && (
                    <Badge color="green" className="text-xs">
                      <Check className="h-3 w-3 mr-1" />
                      Default
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  Expires {method.card?.expMonth}/{method.card?.expYear}
                </p>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
} 