"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON>, RefreshCw, Crown } from "lucide-react";
import { CustomButton } from "@/components/CustomButton";
import { Spinner } from "@/components/Spinner";
import { PolarActiveSubscriptions } from "./PolarActiveSubscriptions";
import { PaymentMethods } from "./PaymentMethods";
import { useUserBillingStatus } from "@/hooks/useUserBillingStatus";
import { useUpgradeToProDialog } from "@/hooks/useUpgradeToProDialog";
import { clientEnv } from "@/env/client";

export function SettingsTabBilling() {
  const isPolarEnabled = clientEnv.NEXT_PUBLIC_ENABLE_POLAR;
  const { billingState, isPro, isLoading, error, refresh } =
    useUserBillingStatus({ enabled: isPolarEnabled });
  const { openUpgradeDialog } = useUpgradeToProDialog();

  const handleManageBilling = () => {
    window.open("/api/auth/portal", "_blank");
  };

  // If Polar is not enabled, show a message
  if (!isPolarEnabled) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Billing Information</h3>
        </div>
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Billing management is not available in this configuration.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Billing Information</h3>
        <CustomButton size="sm" leftIcon={RefreshCw} onClick={refresh}>
          Refresh
        </CustomButton>
      </div>

      {isLoading && <Spinner />}
      {error && !isLoading && (
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">
            {error instanceof Error
              ? error.message
              : "Failed to load billing information"}
          </p>
          <CustomButton size="sm" onClick={refresh} variant="outline">
            Try Again
          </CustomButton>
        </div>
      )}

      {!isLoading && !error && (
        <>
          <PolarActiveSubscriptions
            subscriptions={billingState?.activeSubscriptions || []}
          />
          
          {billingState?.paymentMethods && (
            <PaymentMethods paymentMethods={billingState.paymentMethods} />
          )}
        </>
      )}

      {!isLoading &&
        !error &&
        billingState?.activeSubscriptions &&
        billingState.activeSubscriptions.length > 0 && <hr className="my-4" />}

      {!isLoading && !error && (
        <div className="horizontal gap-2">
          {isPro ? (
            <CustomButton leftIcon={CreditCard} onClick={handleManageBilling}>
              Manage Billing
            </CustomButton>
          ) : (
            <CustomButton
              variant="outline"
              leftIcon={Crown}
              onClick={openUpgradeDialog}
            >
              Upgrade to Pro
            </CustomButton>
          )}
        </div>
      )}

      {!billingState && !isLoading && !error && (
        <p className="text-muted-foreground">
          No billing information or active subscriptions found.
        </p>
      )}
    </div>
  );
}
