"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Crown, Check } from "lucide-react";
import { CustomButton } from "@/components/CustomButton";
import { getActivePaymentPlans } from "@/lib/payment-utils";
import { clientEnv } from "@/env/client";
import { useUserBillingStatus } from "@/hooks/useUserBillingStatus";
import { toast } from "sonner";

interface ChangePlanDialogProps {
  close: () => void;
}

export function ChangePlanDialog({ close }: ChangePlanDialogProps) {
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const plans = getActivePaymentPlans();
  const { billingState, forceRefresh } = useUserBillingStatus({ enabled: true });

  // Find the monthly plans
  const proMonthlyPlan = plans.find(plan => plan.slug === "pro-monthly");
  const unlimitedMonthlyPlan = plans.find(plan => plan.slug === "unlimited-monthly");

  // Determine current plan
  const activeSubscription = billingState?.activeSubscriptions?.[0];
  const currentPlanAmount = activeSubscription?.amount || 0;
  
  const getCurrentPlanSlug = () => {
    if (currentPlanAmount >= 2900) return "unlimited-monthly";
    if (currentPlanAmount >= 900) return "pro-monthly";
    return "trial";
  };

  const currentPlanSlug = getCurrentPlanSlug();

  const handlePlanClick = async (planSlug: string, planName: string) => {
    if (planSlug === currentPlanSlug) {
      return; // Already on this plan
    }

    setLoadingPlan(planSlug);
    
    try {
      // Check if Polar is enabled
      if (!clientEnv.NEXT_PUBLIC_ENABLE_POLAR) {
        alert("Development Mode: Polar payments are disabled. You can test the subscription features with mock data. Check the console for more details.");
        console.log("Polar disabled - using mock data for development");
        setLoadingPlan(null);
        return;
      }

      // Check if user has an active subscription (needs upgrade)
      const hasActiveSubscription = billingState?.activeSubscriptions && billingState.activeSubscriptions.length > 0;

      if (hasActiveSubscription) {
        // Use upgrade endpoint for existing subscribers - this directly updates the subscription
        const response = await fetch('/api/subscription/upgrade', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            planSlug: planSlug.replace('-monthly', '') // Remove -monthly suffix for API
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to upgrade subscription');
        }

        const data = await response.json();
        
        // Show success message and refresh billing state
        toast.success(`Successfully upgraded to ${planName}! Refreshing billing information...`);
        
        // Refresh billing state to show new plan
        setTimeout(() => {
          forceRefresh();
        }, 1000);
        
        // Close dialog after successful upgrade
        setTimeout(() => {
          close();
        }, 2000);
        
      } else {
        // Use regular checkout for new subscribers
        const checkoutUrl = `/api/auth/checkout/${planSlug}`;
        window.location.href = checkoutUrl;
      }
      
    } catch (error) {
      console.error("Error upgrading subscription:", error);
      
      // Show user-friendly error message
      toast.error("Sorry, there was an issue upgrading your plan. Please try again or contact support.");
      
    } finally {
      setLoadingPlan(null);
    }
  };

  const planConfigs = [
    {
      name: "Pro",
      slug: proMonthlyPlan?.slug,
      price: "$9",
      description: "Perfect for small to medium businesses",
      features: [
        "50 route optimizations per month",
        "Up to 55 stops per route",
        "500 image uploads per month",
        "Advanced analytics",
        "Export routes",
        "Email support"
      ],
      cardClassName: "border-2",
      buttonVariant: "outline" as const,
      isCurrentPlan: currentPlanSlug === "pro-monthly"
    },
    {
      name: "Unlimited",
      slug: unlimitedMonthlyPlan?.slug,
      price: "$29",
      description: "For large businesses with unlimited needs",
      features: [
        "Unlimited route optimizations",
        "Up to 100 stops per route",
        "Unlimited image uploads",
        "Advanced analytics",
        "Export routes",
        "Priority support"
      ],
      cardClassName: "border-2 border-blue-500 relative",
      buttonVariant: "filled" as const,
      isCurrentPlan: currentPlanSlug === "unlimited-monthly"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Change Your Plan</h2>
        <p className="text-muted-foreground mt-2">
          Choose the plan that best fits your needs
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {planConfigs.map((plan) => (
          <Card key={plan.name} className={plan.cardClassName}>
            {plan.name === "Unlimited" && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge color="blue" className="bg-blue-500 text-white">Most Popular</Badge>
              </div>
            )}
            
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2">
                {plan.name === "Unlimited" && <Crown className="h-5 w-5 text-blue-500" />}
                {plan.name}
              </CardTitle>
              <div className="text-3xl font-bold">{plan.price}<span className="text-lg font-normal text-muted-foreground">/month</span></div>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <ul className="space-y-3">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              <CustomButton 
                variant={plan.buttonVariant}
                className="w-full gap-2"
                size="lg"
                onClick={() => handlePlanClick(plan.slug!, plan.name)}
                disabled={loadingPlan === plan.slug || plan.isCurrentPlan}
                loading={loadingPlan === plan.slug}
                leftIcon={plan.name === "Unlimited" ? Crown : undefined}
              >
                {plan.isCurrentPlan 
                  ? "Current Plan" 
                  : loadingPlan === plan.slug 
                    ? "Loading..." 
                    : `Switch to ${plan.name}`
                }
              </CustomButton>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Plan upgrades are processed instantly. New subscribers will be redirected to checkout.
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          Your new plan benefits will be available immediately after upgrade.
        </p>
      </div>
    </div>
  );
} 