import { cn } from "@/lib/utils";

interface SectionSeparatorProps {
  className?: string;
  variant?: "subtle" | "gradient" | "visible" | "none";
}

export function SectionSeparator({ 
  className, 
  variant = "subtle" 
}: SectionSeparatorProps) {
  if (variant === "none") return null;
  
  if (variant === "gradient") {
    return (
      <div className={cn("relative w-full h-px", className)}>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-border/20 to-transparent" />
      </div>
    );
  }
  
  return (
    <div className={cn(
      "w-full h-px",
      variant === "subtle" && "bg-gradient-to-r from-transparent via-border/10 to-transparent",
      variant === "visible" && "border-t border-border/30",
      className
    )} />
  );
} 