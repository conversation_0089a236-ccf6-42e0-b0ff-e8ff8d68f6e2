"use client";

import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef } from "react";
import { use<PERSON>itze<PERSON> } from "@/components/KitzeUIContext";

interface PrefetchOptions {
  priority?: "high" | "low";
  delay?: number; // Delay in ms before prefetching
}

export function usePrefetchStrategy() {
  const router = useRouter();
  const { isMobile } = useKitzeUI();
  const prefetchedPages = useRef(new Set<string>());
  const prefetchTimeouts = useRef(new Map<string, NodeJS.Timeout>());

  // Prefetch a single page
  const prefetchPage = useCallback((href: string, options: PrefetchOptions = {}) => {
    if (prefetchedPages.current.has(href)) {
      return; // Already prefetched
    }

    const { delay = 0 } = options;

    // Clear any existing timeout for this href
    const existingTimeout = prefetchTimeouts.current.get(href);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    const prefetchFn = () => {
      router.prefetch(href);
      prefetchedPages.current.add(href);
      prefetchTimeouts.current.delete(href);
    };

    if (delay > 0) {
      const timeout = setTimeout(prefetchFn, delay);
      prefetchTimeouts.current.set(href, timeout);
    } else {
      prefetchFn();
    }
  }, [router]);

  // Prefetch multiple pages
  const prefetchPages = useCallback((hrefs: string[], options: PrefetchOptions = {}) => {
    hrefs.forEach((href, index) => {
      // Stagger prefetching to avoid overwhelming the network
      const delay = (options.delay || 0) + (index * 100);
      prefetchPage(href, { ...options, delay });
    });
  }, [prefetchPage]);

  // Prefetch on hover (desktop only)
  const prefetchOnHover = useCallback((href: string) => {
    if (!isMobile) {
      prefetchPage(href, { delay: 50 }); // Small delay to avoid prefetching on quick mouse movements
    }
  }, [isMobile, prefetchPage]);

  // Prefetch immediately (for mobile menus, etc.)
  const prefetchImmediately = useCallback((href: string) => {
    prefetchPage(href);
  }, [prefetchPage]);

  // Prefetch critical pages on app load
  const prefetchCriticalPages = useCallback(() => {
    const criticalPages = ["/routes", "/settings", "/analytics"];
    prefetchPages(criticalPages, { priority: "high", delay: 1000 });
  }, [prefetchPages]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      prefetchTimeouts.current.forEach(timeout => clearTimeout(timeout));
      prefetchTimeouts.current.clear();
    };
  }, []);

  return {
    prefetchPage,
    prefetchPages,
    prefetchOnHover,
    prefetchImmediately,
    prefetchCriticalPages,
    isPrefetched: (href: string) => prefetchedPages.current.has(href),
  };
} 