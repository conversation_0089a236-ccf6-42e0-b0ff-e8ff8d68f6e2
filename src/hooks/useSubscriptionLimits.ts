import { useMemo } from 'react';
import { useUserBillingStatus } from './useUserBillingStatus';
import { useUserAnalytics } from './useUserAnalytics';
import { authClient } from '@/server/auth/client';
import { 
  SubscriptionLimitsService, 
  PlanTier, 
  type LimitCheckResult,
  type PlanLimits,
  type UsageStats
} from '@/lib/subscription-limits';

export interface SubscriptionLimitsHook {
  // Plan information
  planTier: PlanTier;
  planLimits: PlanLimits;
  isPro: boolean;
  isUnlimited: boolean;
  
  // Usage statistics
  usage: {
    routesOptimizedThisMonth: number;
    totalRoutes: number;
    activeRoutes: number;
  } | null;
  
  // Limit checking functions
  checkRouteOptimization: (stopsCount: number) => LimitCheckResult;
  checkImageUpload: (imageCount: number) => LimitCheckResult;
  checkFeatureAccess: (feature: 'bulkOptimization' | 'exportRoutes' | 'advancedAnalytics') => LimitCheckResult;
  
  // Utility functions
  getUsagePercentage: (current: number, limit: number) => number;
  getRemainingUsage: (current: number, limit: number) => number;
  isNearLimit: (current: number, limit: number, threshold?: number) => boolean;
  getUpgradeMessage: (feature?: string) => string;
  
  // Loading states
  isLoading: boolean;
  error: any;
}

export function useSubscriptionLimits(): SubscriptionLimitsHook {
  const { data: session } = authClient.useSession();
  const { billingState, isPro, isLoading: billingLoading, error: billingError } = useUserBillingStatus({ enabled: !!session });
  const { analytics, isLoading: analyticsLoading, error: analyticsError } = useUserAnalytics();
  
  // Determine plan tier based on billing state
  const planTier = useMemo((): PlanTier => {
    if (!billingState || !isPro) {
      return PlanTier.TRIAL;
    }
    
    // Check if user has unlimited plan ($29+ monthly or $290+ annual)
    const hasUnlimitedSubscription = billingState.activeSubscriptions?.some(
      sub => sub.amount >= 2900 // $29+ indicates unlimited plan
    );
    
    if (hasUnlimitedSubscription) {
      return PlanTier.UNLIMITED;
    }
    
    return isPro ? PlanTier.PRO : PlanTier.TRIAL;
  }, [billingState, isPro]);
  
  const planLimits = useMemo(() => {
    return SubscriptionLimitsService.getPlanLimits(planTier);
  }, [planTier]);
  
  // Use real user analytics data instead of mock data
  const usageStats: UsageStats = useMemo(() => {
    if (!analytics) {
      // Return zero usage for new users or when data is loading
      return {
        routesOptimizedThisMonth: 0,
        geocodingRequestsToday: 0,
        optimizationRequestsThisHour: 0,
        savedRoutesCount: 0,
      };
    }
    
    return {
      routesOptimizedThisMonth: analytics.routesThisMonth,
      geocodingRequestsToday: 0, // This would come from a separate API in a real app
      optimizationRequestsThisHour: 0, // This would come from a separate API in a real app
      savedRoutesCount: analytics.totalRoutes,
    };
  }, [analytics]);
  
  const checkRouteOptimization = useMemo(() => {
    return (stopsCount: number): LimitCheckResult => {
      return SubscriptionLimitsService.checkRouteOptimizationLimit(
        planTier,
        usageStats,
        stopsCount
      );
    };
  }, [planTier, usageStats]);
  
  const checkImageUpload = useMemo(() => {
    return (imageCount: number): LimitCheckResult => {
      return SubscriptionLimitsService.checkImageUploadLimit(planTier, imageCount);
    };
  }, [planTier]);
  
  const checkFeatureAccess = useMemo(() => {
    return (feature: 'bulkOptimization' | 'exportRoutes' | 'advancedAnalytics'): LimitCheckResult => {
      const featureMap = {
        bulkOptimization: 'canUseBulkOptimization' as const,
        exportRoutes: 'canExportRoutes' as const,
        advancedAnalytics: 'canUseAdvancedAnalytics' as const,
      };
      
      return SubscriptionLimitsService.checkFeatureAccess(planTier, featureMap[feature]);
    };
  }, [planTier]);
  
  const getUsagePercentage = useMemo(() => {
    return (current: number, limit: number): number => {
      return SubscriptionLimitsService.getUsagePercentage(current, limit);
    };
  }, []);
  
  const getRemainingUsage = useMemo(() => {
    return (current: number, limit: number): number => {
      return SubscriptionLimitsService.getRemainingUsage(current, limit);
    };
  }, []);
  
  const isNearLimit = useMemo(() => {
    return (current: number, limit: number, threshold = 0.8): boolean => {
      return SubscriptionLimitsService.isNearLimit(current, limit, threshold);
    };
  }, []);
  
  const getUpgradeMessage = useMemo(() => {
    return (feature?: string): string => {
      return SubscriptionLimitsService.getUpgradeMessage(planTier, feature);
    };
  }, [planTier]);
  
  const isLoading = billingLoading || analyticsLoading;
  const error = billingError || analyticsError;
  
  return {
    // Plan information
    planTier,
    planLimits,
    isPro,
    isUnlimited: planTier === PlanTier.UNLIMITED,
    
    // Usage statistics (real user data)
    usage: analytics ? {
      routesOptimizedThisMonth: usageStats.routesOptimizedThisMonth,
      totalRoutes: usageStats.savedRoutesCount,
      activeRoutes: Math.floor(usageStats.savedRoutesCount * 0.6), // Mock active routes calculation
    } : null,
    
    // Limit checking functions
    checkRouteOptimization,
    checkImageUpload,
    checkFeatureAccess,
    
    // Utility functions
    getUsagePercentage,
    getRemainingUsage,
    isNearLimit,
    getUpgradeMessage,
    
    // Loading states
    isLoading,
    error,
  };
} 