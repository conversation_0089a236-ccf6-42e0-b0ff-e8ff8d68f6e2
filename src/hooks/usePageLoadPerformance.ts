"use client";

import { useEffect, useRef } from "react";
import { usePathname } from "next/navigation";

export function usePageLoadPerformance() {
  const pathname = usePathname();
  const navigationStartRef = useRef<number | null>(null);
  const performanceDataRef = useRef<Record<string, number>>({});

  useEffect(() => {
    // Record navigation start time
    navigationStartRef.current = performance.now();

    // Measure page load completion
    const measurePageLoad = () => {
      if (navigationStartRef.current) {
        const loadTime = performance.now() - navigationStartRef.current;
        performanceDataRef.current[pathname] = loadTime;
        
        // Log performance data in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`🚀 Page load performance for ${pathname}: ${loadTime.toFixed(2)}ms`);
          
          // Show prefetch effectiveness
          if (loadTime < 100) {
            console.log(`✅ Fast load! Likely prefetched.`);
          } else if (loadTime < 500) {
            console.log(`⚡ Good load time.`);
          } else {
            console.log(`⏳ Slow load. Consider prefetching.`);
          }
        }
      }
    };

    // Measure when page is interactive
    const timer = setTimeout(measurePageLoad, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [pathname]);

  const getPerformanceData = () => performanceDataRef.current;

  const getAverageLoadTime = () => {
    const times = Object.values(performanceDataRef.current);
    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  };

  return {
    getPerformanceData,
    getAverageLoadTime,
  };
} 