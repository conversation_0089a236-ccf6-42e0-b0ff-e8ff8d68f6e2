import { useCallback } from "react";
import { api } from "@/trpc/react";
import { clientEnv } from "@/env/client";

export function useUserBillingStatus({
  enabled = false,
}: { enabled?: boolean } = {}) {
  const utils = api.useUtils();
  
  // Only enable the query if Polar is enabled and the enabled flag is true
  const shouldQuery = enabled && clientEnv.NEXT_PUBLIC_ENABLE_POLAR;
  
  const {
    data: billingState,
    isLoading,
    error,
    refetch,
  } = api.polar.getBillingState.useQuery(undefined, {
    enabled: shouldQuery,
    retry: false, // Don't retry on error
    staleTime: 30 * 1000, // Consider data stale after 30 seconds
  });

  const refresh = useCallback(() => {
    if (shouldQuery) {
      void utils.polar.getBillingState.invalidate();
    }
  }, [utils, shouldQuery]);

  const forceRefresh = useCallback(async () => {
    if (shouldQuery) {
      // Clear cache and refetch immediately
      await utils.polar.getBillingState.invalidate();
      return refetch();
    }
  }, [utils, shouldQuery, refetch]);

  const isPro = billingState?.isPro ?? false;

  return {
    billingState,
    isLoading: shouldQuery ? isLoading : false,
    error: shouldQuery ? error : null,
    isPro,
    refresh,
    forceRefresh,
    isPolarEnabled: clientEnv.NEXT_PUBLIC_ENABLE_POLAR,
  };
}
