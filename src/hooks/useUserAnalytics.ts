"use client";

import { useState, useEffect } from "react";
import { UserAnalyticsService, type UserAnalytics, type RouteOptimizationRecord } from "@/lib/user-analytics";
import { useCurrentUser } from "@/hooks/useCurrentUser";

export function useUserAnalytics() {
  const [analytics, setAnalytics] = useState<UserAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const user = useCurrentUser();

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const data = await UserAnalyticsService.getUserAnalytics(user.id);
        setAnalytics(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [user?.id]);

  const refreshAnalytics = async () => {
    if (!user?.id) return;
    
    try {
      setError(null);
      const data = await UserAnalyticsService.getUserAnalytics(user.id);
      setAnalytics(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh analytics');
    }
  };

  return {
    analytics,
    isLoading,
    error,
    refreshAnalytics
  };
}

export function useRecentRoutes(limit: number = 10) {
  const [routes, setRoutes] = useState<RouteOptimizationRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const user = useCurrentUser();

  useEffect(() => {
    const fetchRoutes = async () => {
      if (!user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const data = await UserAnalyticsService.getRecentRoutes(user.id, limit);
        setRoutes(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch routes');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoutes();
  }, [user?.id, limit]);

  const refreshRoutes = async () => {
    if (!user?.id) return;
    
    try {
      setError(null);
      const data = await UserAnalyticsService.getRecentRoutes(user.id, limit);
      setRoutes(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh routes');
    }
  };

  return {
    routes,
    isLoading,
    error,
    refreshRoutes
  };
} 