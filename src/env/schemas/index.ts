import { z } from "zod";
import * as authSchemas from "./auth";
import * as githubSchemas from "./github";
import * as googleSchemas from "./google";
import * as emailSchema from "./email";
import * as generalSchemas from "./general";
import * as polarSchemas from "./polar";
import * as uploadThingSchemas from "./uploadthing";
import * as redisSchemas from "./redis";
import * as routeOptimizationSchemas from "./route-optimization";

export const clientSchema = z
  .object({})
  .merge(githubSchemas.clientSchema)
  .merge(googleSchemas.clientSchema)
  .merge(authSchemas.clientSchema)
  .merge(redisSchemas.clientSchema)
  .merge(emailSchema.clientSchema)
  .merge(generalSchemas.clientSchema)
  .merge(polarSchemas.clientSchema)
  .merge(uploadThingSchemas.clientSchema)
  .merge(routeOptimizationSchemas.clientSchema);

export const clientEnvRaw: {
  [k in keyof z.infer<typeof clientSchema>]: string | undefined;
} = {
  NODE_ENV: process.env.NODE_ENV,
  NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
  NEXT_PUBLIC_APP_DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION,
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION:
    process.env.NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION,
  NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION:
    process.env.NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION,
  NEXT_PUBLIC_EMAIL_PROVIDER: process.env.NEXT_PUBLIC_EMAIL_PROVIDER,
  NEXT_PUBLIC_EMAIL_ENABLE_EMAIL_PREVIEW:
    process.env.NEXT_PUBLIC_EMAIL_ENABLE_EMAIL_PREVIEW,
  NEXT_PUBLIC_ENABLE_POLAR: process.env.NEXT_PUBLIC_ENABLE_POLAR,
  NEXT_PUBLIC_POLAR_ENV: process.env.NEXT_PUBLIC_POLAR_ENV,
  NEXT_PUBLIC_ENABLE_UPLOADTHING: process.env.NEXT_PUBLIC_ENABLE_UPLOADTHING,
  NEXT_PUBLIC_ENABLE_BACKGROUND_JOBS:
    process.env.NEXT_PUBLIC_ENABLE_BACKGROUND_JOBS,
  NEXT_PUBLIC_ENABLE_CRON: process.env.NEXT_PUBLIC_ENABLE_CRON,
  NEXT_PUBLIC_EMAIL_PREVIEW_OPEN_TAB:
    process.env.NEXT_PUBLIC_EMAIL_PREVIEW_OPEN_TAB,
  NEXT_PUBLIC_EMAIL_PREVIEW_OPEN_SIMULATOR:
    process.env.NEXT_PUBLIC_EMAIL_PREVIEW_OPEN_SIMULATOR,
  NEXT_PUBLIC_UPLOADTHING_URL_ROOT:
    process.env.NEXT_PUBLIC_UPLOADTHING_URL_ROOT,
  NEXT_PUBLIC_ENABLE_BLOG_PAGE: process.env.NEXT_PUBLIC_ENABLE_BLOG_PAGE,
  NEXT_PUBLIC_ENABLE_ABOUT_PAGE: process.env.NEXT_PUBLIC_ENABLE_ABOUT_PAGE,
  NEXT_PUBLIC_ENABLE_CHAT_PAGE: process.env.NEXT_PUBLIC_ENABLE_CHAT_PAGE,
  NEXT_PUBLIC_ENABLE_PRICING_PAGE: process.env.NEXT_PUBLIC_ENABLE_PRICING_PAGE,
  NEXT_PUBLIC_AUTH_ENABLE_EMAIL_VERIFICATION:
    process.env.NEXT_PUBLIC_AUTH_ENABLE_EMAIL_VERIFICATION,
  NEXT_PUBLIC_AUTH_ENABLE_EMAIL_PASSWORD_AUTHENTICATION:
    process.env.NEXT_PUBLIC_AUTH_ENABLE_EMAIL_PASSWORD_AUTHENTICATION,
  NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN,
  NEXT_PUBLIC_HERE_API_KEY: process.env.NEXT_PUBLIC_HERE_API_KEY,
  NEXT_PUBLIC_OPENROUTER_API_KEY: process.env.NEXT_PUBLIC_OPENROUTER_API_KEY,
};

export const serverSchema = clientSchema
  .and(generalSchemas.serverSchema)
  .and(emailSchema.serverSchema)
  .and(githubSchemas.serverSchema)
  .and(googleSchemas.serverSchema)
  .and(authSchemas.authServerSchema)
  .and(polarSchemas.serverSchema)
  .and(uploadThingSchemas.serverSchema)
  .and(redisSchemas.serverSchema)
  .and(routeOptimizationSchemas.serverSchema)
  // Check if there's at least one sign-up method (Production)
  .refine(
    (env) => {
      if (process.env.NODE_ENV !== "production") {
        return true;
      }
      const hasGithub = env.NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION;
      const hasGoogle = env.NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION;
      const hasEmailPassword =
        env.NEXT_PUBLIC_AUTH_ENABLE_EMAIL_PASSWORD_AUTHENTICATION &&
        env.NEXT_PUBLIC_EMAIL_PROVIDER !== "none";

      if (!hasGithub && !hasGoogle && !hasEmailPassword) {
        return false;
      }
      return true;
    },
    {
      message:
        "Production mode requires at least one sign-up method: enable GitHub integration (NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION), Google integration (NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION), or Email/Password authentication (NEXT_PUBLIC_AUTH_ENABLE_EMAIL_PASSWORD_AUTHENTICATION)",
    },
  )
  // Check if there's at least one sign-up method (Dev)
  .refine(
    (env) => {
      if (process.env.NODE_ENV !== "development") {
        return true;
      }
      const hasGithub = env.NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION;
      const hasGoogle = env.NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION;
      const hasEmailPassword =
        env.NEXT_PUBLIC_AUTH_ENABLE_EMAIL_PASSWORD_AUTHENTICATION &&
        (env.NEXT_PUBLIC_EMAIL_PROVIDER !== "none" ||
          env.NEXT_PUBLIC_EMAIL_ENABLE_EMAIL_PREVIEW);

      if (!hasGithub && !hasGoogle && !hasEmailPassword) {
        return false;
      }
      return true;
    },
    {
      message:
        "Development mode requires at least one sign-up method: enable GitHub integration (NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION), Google integration (NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION), or Email/Password authentication (NEXT_PUBLIC_AUTH_ENABLE_EMAIL_PASSWORD_AUTHENTICATION)",
    },
  );
