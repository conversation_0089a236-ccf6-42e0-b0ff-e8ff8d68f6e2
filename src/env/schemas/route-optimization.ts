import { z } from "zod";

// Route optimization schema for client-side environment variables
export const clientSchema = z.object({
  NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN: z.string().optional(),
  NEXT_PUBLIC_HERE_API_KEY: z.string().optional(),
  NEXT_PUBLIC_OPENROUTER_API_KEY: z.string().optional(),
});

// Server-side schema (if needed for server-side route optimization)
export const serverSchema = z.object({
  MAPBOX_API_KEY: z.string().optional(),
  HERE_API_KEY: z.string().optional(),
  OPENROUTER_API_KEY: z.string().optional(),
});

export type ClientSchema = z.infer<typeof clientSchema>;
export type ServerSchema = z.infer<typeof serverSchema>; 