import { object, type z, string } from "zod";
import {
  getEmailSchema,
  getPasswordSchema,
} from "./shared-schemas";

export const signUpSchema = object({
  email: getEmailSchema(),
  password: getPasswordSchema("password"),
  confirmPassword: string().min(1, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export type SignUpSchemaType = z.infer<typeof signUpSchema>;
