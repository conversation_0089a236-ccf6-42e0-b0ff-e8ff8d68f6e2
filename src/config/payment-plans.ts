export enum PlanType {
  Subscription = "subscription",
  OneTime = "one-time",
}

export enum SubscriptionInterval {
  Monthly = "monthly",
  Annual = "annual",
}

export interface BasePlanInfo {
  slug: string;
  name: string;
  description?: string;
  type: PlanType;
  interval?: SubscriptionInterval;
  price: string;
  priceAmount: number;
}

interface BasePlanWithId extends BasePlanInfo {
  productId: string;
}

export interface SubscriptionPlan extends BasePlanWithId {
  type: PlanType.Subscription;
  interval: SubscriptionInterval;
}

export interface OneTimePlan extends BasePlanWithId {
  type: PlanType.OneTime;
}

export type PaymentPlan = SubscriptionPlan | OneTimePlan;

export const basePlans: BasePlanInfo[] = [
  {
    slug: "pro-monthly",
    name: "Pro Monthly",
    description: "Perfect for small to medium businesses. 7-day free trial included.",
    type: PlanType.Subscription,
    interval: SubscriptionInterval.Monthly,
    price: "$9",
    priceAmount: 9,
  },
  {
    slug: "unlimited-monthly",
    name: "Unlimited Monthly",
    description: "For large businesses with unlimited needs. 7-day free trial included.",
    type: PlanType.Subscription,
    interval: SubscriptionInterval.Monthly,
    price: "$29",
    priceAmount: 29,
  },
];

export const productIdsSandbox: Record<string, string> = {
  "pro-monthly": "9861a6cf-914d-4693-84cd-946e5f5a44dc",
  "unlimited-monthly": "6fd5edc8-7215-4fea-815f-2ff8ddbe6e14",
};

export const productIdsProduction: Record<string, string> = {
  "pro-monthly": "",
  "unlimited-monthly": "",
};

export const createPlansForEnv = (
  productIds: Record<string, string>,
): PaymentPlan[] => {
  return basePlans
    .map((basePlan: BasePlanInfo) => {
      const productId = productIds[basePlan.slug];
      if (!productId) {
        console.warn(
          `Product ID not found for slug '${basePlan.slug}' in the current environment.`,
        );
        return null;
      }

      if (basePlan.type === PlanType.Subscription) {
        if (!basePlan.interval) {
          console.error(
            `Subscription plan '${basePlan.slug}' is missing an interval.`,
          );
          return null;
        }
        return {
          ...basePlan,
          productId,
          type: PlanType.Subscription,
          interval: basePlan.interval,
        } as SubscriptionPlan;
      } else {
        return {
          ...basePlan,
          productId,
          type: PlanType.OneTime,
        } as OneTimePlan;
      }
    })
    .filter((plan): plan is PaymentPlan => plan !== null);
};

export const getPaymentPlans = (
  environment: "sandbox" | "production",
): PaymentPlan[] => {
  return environment === "production"
    ? createPlansForEnv(productIdsProduction)
    : createPlansForEnv(productIdsSandbox);
};
