import { unstable_ViewTransition as ViewTransition } from "react";
import LandingPageHeader from "./home/<USER>/LandingPageHeader";

export default function LandingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="bg-background flex min-h-screen flex-col">
      <LandingPageHeader />
      <main className="flex-1 pt-16">
        <ViewTransition name="page">{children}</ViewTransition>
      </main>
    </div>
  );
}
