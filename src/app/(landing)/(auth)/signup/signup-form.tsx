"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm, FormProvider } from "react-hook-form";
import { Button } from "@/components/ui/enhanced-button";
import { FormFieldInput } from "@/components/FormFieldInput";
import { LoginWithGitHub } from "@/components/auth/LoginWithGitHub";
import { LoginWithGoogle } from "@/components/auth/LoginWithGoogle";
import { signUpSchema, type SignUpSchemaType } from "@/schemas/signup-schema";
import { EmailSentAnimation } from "./success-animation";
import {
  <PERSON><PERSON><PERSON><PERSON>rap<PERSON>,
  AnimatedFormHeader,
  AnimatedForm<PERSON>ontent,
  AnimatedFormFields,
  AnimatedSuccessContent,
} from "@/components/auth/AnimatedFormComponents";
import { showEmailPasswordFields, hasGithubIntegration, hasGoogleIntegration } from "@/config/config";

interface SignupFormProps {
  className?: string;
  onSubmit: (values: SignUpSchemaType) => Promise<void>;
  isLoading: boolean;
  isSuccess: boolean;
}

export function SignupForm({
  className,
  onSubmit,
  isLoading,
  isSuccess,
  ...props
}: SignupFormProps) {
  const form = useForm<SignUpSchemaType>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const handleSubmit = form.handleSubmit(onSubmit);

  return (
    <AnimatedFormWrapper className={className} {...props}>
      <AnimatedFormHeader
        title="Create an account"
        description="Enter your details to get started"
      />
      <AnimatedFormContent>
        <AnimatedFormFields isVisible={!isSuccess}>
          <FormProvider {...form}>
            <form onSubmit={handleSubmit} className="space-y-6">
              {showEmailPasswordFields && (
                <>
                  <FormFieldInput
                    name="email"
                    label="Email"
                    type="email"
                    placeholder="Enter your email"
                  />

                  <FormFieldInput
                    name="password"
                    label="Password"
                    type="password"
                    placeholder="Create a password"
                  />

                  <FormFieldInput
                    name="confirmPassword"
                    label="Confirm Password"
                    type="password"
                    placeholder="Confirm your password"
                  />
                </>
              )}

              <div className="space-y-3">
                {showEmailPasswordFields && (
                  <Button
                    loading={isLoading}
                    className="w-full"
                    type="submit"
                  >
                    Create Account
                  </Button>
                )}
                
                {(hasGithubIntegration || hasGoogleIntegration) && showEmailPasswordFields && (
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-2 text-muted-foreground">
                        Or continue with
                      </span>
                    </div>
                  </div>
                )}
                
                <div className="grid gap-2">
                  {hasGithubIntegration && <LoginWithGitHub label="Sign up with GitHub" />}
                  {hasGoogleIntegration && <LoginWithGoogle label="Sign up with Google" />}
                </div>
              </div>
              
              {showEmailPasswordFields && (
                <div className="mt-4 text-center text-sm">
                  Already have an account?{" "}
                  <Link href="/signin" className="underline underline-offset-4">
                    Sign in
                  </Link>
                </div>
              )}
            </form>
          </FormProvider>
        </AnimatedFormFields>

        <AnimatedSuccessContent isVisible={isSuccess}>
          <EmailSentAnimation />
        </AnimatedSuccessContent>
      </AnimatedFormContent>
    </AnimatedFormWrapper>
  );
}
