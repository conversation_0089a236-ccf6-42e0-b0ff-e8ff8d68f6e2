"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm, FormProvider } from "react-hook-form";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/enhanced-button";
import { FormFieldInput } from "@/components/FormFieldInput";
import { FormFieldCheckbox } from "@/components/FormFieldCheckbox";
import { LoginWithGoogle } from "@/components/auth/LoginWithGoogle";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { signInSchema, type SignInSchemaType } from "@/schemas/login-schema";
import { signUpSchema, type SignUpSchemaType } from "@/schemas/signup-schema";
import { MapPin } from "lucide-react";
import { showEmailPasswordFields, hasGoogleIntegration } from "@/config/config";

interface AuthFormProps {
  className?: string;
  onSignIn: (values: SignInSchemaType) => Promise<void>;
  onSignUp: (values: SignUpSchemaType) => Promise<void>;
  isLoading: boolean;
  initialTab?: "signin" | "signup";
}

export function SigninForm({
  className,
  onSignIn,
  onSignUp,
  isLoading,
  initialTab = "signin",
  ...props
}: AuthFormProps) {
  const [activeTab, setActiveTab] = useState<"signin" | "signup">(initialTab);

  const signInForm = useForm<SignInSchemaType>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const signUpForm = useForm<SignUpSchemaType>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const handleSignInSubmit = signInForm.handleSubmit(onSignIn);
  const handleSignUpSubmit = signUpForm.handleSubmit(onSignUp);

  return (
    <div className={cn("w-full max-w-md mx-auto", className)} {...props}>
      <Card className="border-0 bg-background shadow-lg">
        <CardContent className="p-8">
          {/* Logo and Header */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold mb-2">Get started</h1>
            <p className="text-muted-foreground">
              Transform your delivery operations with intelligent route optimization.
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="flex mb-8 bg-muted/30 rounded-lg p-1">
            <button
              type="button"
              onClick={() => setActiveTab("signin")}
              className={cn(
                "flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors",
                activeTab === "signin"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              Log in
            </button>
            <button
              type="button"
              onClick={() => setActiveTab("signup")}
              className={cn(
                "flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors",
                activeTab === "signup"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              Create account
            </button>
          </div>

          {/* Sign In Form */}
          {activeTab === "signin" && (
            <FormProvider {...signInForm}>
              <form onSubmit={handleSignInSubmit} className="space-y-6">
                <FormFieldInput
                  name="email"
                  label="Email"
                  type="email"
                  placeholder="Email"
                />

                <FormFieldInput
                  name="password"
                  label="Password"
                  type="password"
                  placeholder="Password"
                />

                <div className="flex items-center justify-between">
                  <FormFieldCheckbox
                    name="rememberMe"
                    label="Keep me signed in"
                  />
                  <Link
                    href="/forgot-password"
                    className="text-sm text-muted-foreground hover:text-foreground underline-offset-4 hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>

                <Button
                  loading={isLoading}
                  className="w-full"
                  type="submit"
                >
                  Sign In
                </Button>

                {hasGoogleIntegration && (
                  <>
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-border/30" />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-background px-2 text-muted-foreground">
                          OR
                        </span>
                      </div>
                    </div>

                    <LoginWithGoogle className="w-full" />
                  </>
                )}
              </form>
            </FormProvider>
          )}

          {/* Sign Up Form */}
          {activeTab === "signup" && (
            <FormProvider {...signUpForm}>
              <form onSubmit={handleSignUpSubmit} className="space-y-6">
                <FormFieldInput
                  name="email"
                  label="Email"
                  type="email"
                  placeholder="Email"
                />

                <FormFieldInput
                  name="password"
                  label="Password"
                  type="password"
                  placeholder="Password"
                />

                <FormFieldInput
                  name="confirmPassword"
                  label="Confirm Password"
                  type="password"
                  placeholder="Confirm Password"
                />

                <Button
                  loading={isLoading}
                  className="w-full bg-foreground text-background hover:bg-foreground/90"
                  type="submit"
                >
                  Create account
                </Button>

                {hasGoogleIntegration && (
                  <>
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-border/30" />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-background px-2 text-muted-foreground">
                          OR
                        </span>
                      </div>
                    </div>

                    <LoginWithGoogle 
                      className="w-full" 
                      label="Sign up with Google"
                    />
                  </>
                )}

                <div className="text-center text-sm text-muted-foreground">
                  By creating an account, you agree to our{" "}
                  <Link href="/terms" className="underline underline-offset-4 hover:text-foreground">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link href="/privacy" className="underline underline-offset-4 hover:text-foreground">
                    Privacy Policy
                  </Link>
                </div>
              </form>
            </FormProvider>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
