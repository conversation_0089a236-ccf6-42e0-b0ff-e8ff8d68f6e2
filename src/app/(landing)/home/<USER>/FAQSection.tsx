"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Minus } from "lucide-react";

export default function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const faqs = [
    {
      question: "How much can I save with route optimization?",
      answer: "Most customers see 25-35% reduction in fuel costs and save 3-5 hours of planning time daily. The exact savings depend on your fleet size and current efficiency level."
    },
    {
      question: "How quickly can I get started?",
      answer: "Setup takes less than 5 minutes. Simply upload your delivery addresses, and our AI will immediately start optimizing your routes. No complex integrations required."
    },
    {
      question: "Does it work with my existing system?",
      answer: "Yes! OptiRoute Pro integrates with popular CRM and logistics platforms via API. We also offer CSV import/export for easy data transfer."
    },
    {
      question: "What about real-time changes and delays?",
      answer: "Our system continuously monitors traffic conditions and can instantly re-optimize routes when delays occur. Drivers receive updated instructions in real-time."
    },
    {
      question: "Is there a limit on the number of stops?",
      answer: "No limits! Our platform efficiently handles routes with hundreds of stops. Whether you have 10 or 1000 daily deliveries, we optimize them all."
    },
    {
      question: "What kind of support do you provide?",
      answer: "We offer 24/7 customer support, onboarding assistance, and dedicated account managers for enterprise clients. Plus extensive documentation and video tutorials."
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-3xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold mb-4">
              Frequently asked <span className="text-muted-foreground">questions</span>
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to know about route optimization.
            </p>
          </motion.div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="border-b border-border"
              >
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full py-6 text-left flex items-center justify-between group hover:text-foreground transition-colors"
                >
                  <span className="text-lg font-medium pr-8">
                    {faq.question}
                  </span>
                  <div className="flex-shrink-0">
                    {openItems.includes(index) ? (
                      <Minus className="h-5 w-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                    ) : (
                      <Plus className="h-5 w-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                    )}
                  </div>
                </button>
                
                <AnimatePresence>
                  {openItems.includes(index) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="pb-6 text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
} 