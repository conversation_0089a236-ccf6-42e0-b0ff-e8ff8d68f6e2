"use client";

import { ArrowRight, MapPin, Route, Truck, Users, Plus } from "lucide-react";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/enhanced-button";

export default function HeroSection() {
  const stats = [
    { label: "Active Users", value: "500+", icon: Route },
    { label: "Miles Saved", value: "250k+", icon: Truck },
  ];

  // Mock user avatars for social proof
  const userAvatars = Array.from({ length: 4 }, (_, i) => `/api/placeholder/40/40?text=U${i + 1}`);

  return (
    <section className="relative overflow-hidden bg-background py-20 lg:py-32">
      {/* Background grid */}
      <div className="absolute inset-0 bg-grid-small opacity-5" />
      
      <div className="container relative mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Left Column - Main Content */}
          <div className="space-y-8">
            {/* News Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="inline-flex items-center gap-3 rounded-lg border border-border bg-muted/50 pl-3 pr-4 py-1.5 text-sm">
                <span className="text-foreground">Start optimizing routes today</span>
                <ArrowRight className="h-3 w-3 text-muted-foreground" />
              </div>
            </motion.div>

            {/* Main Headline */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="space-y-4"
            >
              <h1 className="text-5xl lg:text-7xl font-bold leading-tight">
                Introducing the{" "}
                <span className="text-muted-foreground">
                  world's best
                </span>{" "}
                route optimization.
              </h1>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex flex-row gap-4"
            >
              <Button
                href="/signup"
                size="lg"
                leftIcon={ArrowRight}
                className="rounded-md"
              >
                Get Started Free
              </Button>
              
              <Button
                href="/demo"
                variant="outline"
                size="lg"
                className="rounded-md"
              >
                Sign Up
              </Button>
            </motion.div>
          </div>

          {/* Right Column - Description & Stats */}
          <div className="space-y-8 lg:pl-8">
            {/* Description */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="space-y-6"
            >
              <p className="text-xl text-muted-foreground leading-relaxed">
                Transform your delivery operations with the most advanced route optimization platform—
                reduce costs, save time, and delight customers effortlessly.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-6">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                    className="space-y-1"
                  >
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                    <div className="text-3xl font-bold">{stat.value}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* User Avatars */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="flex items-center gap-2"
            >
              <div className="flex -space-x-2">
                {userAvatars.map((avatar, index) => (
                  <div
                    key={index}
                    className="relative h-10 w-10 rounded-full border-2 border-background bg-muted flex items-center justify-center text-sm font-medium"
                  >
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </div>
                ))}
                <div className="relative h-10 w-10 rounded-full border-2 border-background bg-foreground text-background flex items-center justify-center ml-1">
                  <Plus className="h-4 w-4" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
} 