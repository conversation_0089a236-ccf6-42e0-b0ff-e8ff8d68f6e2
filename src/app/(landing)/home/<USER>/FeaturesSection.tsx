"use client";

import { 
  Route, 
  Camera, 
  Zap, 
  DollarSign, 
  Navigation, 
  BarChart3,
  Clock,
  MapPin
} from "lucide-react";
import { motion } from "framer-motion";

export default function FeaturesSection() {
  const features = [
    {
      icon: Route,
      title: "Smart Route Planning",
      description: "AI-powered algorithms create the most efficient routes automatically, considering traffic, distance, and delivery windows.",
    },
    {
      icon: Camera,
      title: "Image Recognition",
      description: "Upload photos of addresses or receipts. Our AI extracts location data instantly, eliminating manual address entry.",
    },
    {
      icon: Zap,
      title: "Real-time Optimization",
      description: "Dynamic route adjustments based on live traffic data, weather conditions, and unexpected delays.",
    },
    {
      icon: DollarSign,
      title: "Cost Reduction",
      description: "Reduce fuel costs by up to 30% and save hours of planning time with optimized routing strategies.",
    },
    {
      icon: Navigation,
      title: "Mobile Navigation",
      description: "Turn-by-turn GPS navigation with offline maps ensures drivers never lose their way.",
    },
    {
      icon: BarChart3,
      title: "Analytics Dashboard",
      description: "Track performance metrics, monitor delivery times, and identify optimization opportunities.",
    },
  ];

  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="mb-4 text-4xl font-bold lg:text-5xl">
              Everything you need for 
              <span className="text-muted-foreground">
                {" "}perfect routes
              </span>
            </h2>
            <p className="text-xl text-muted-foreground">
              Comprehensive route optimization tools designed for modern delivery operations.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group space-y-4"
            >
              <div className="inline-flex rounded-lg bg-muted/30 p-3">
                <feature.icon className="h-6 w-6 text-foreground" />
              </div>
              <h3 className="text-xl font-semibold">{feature.title}</h3>
              <p className="text-muted-foreground leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
} 