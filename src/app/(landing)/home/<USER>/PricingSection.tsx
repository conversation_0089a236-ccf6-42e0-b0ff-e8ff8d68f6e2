"use client";

import { <PERSON>, Crown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { authClient } from "@/server/auth/client";
import { useUserBillingStatus } from "@/hooks/useUserBillingStatus";
import { getActivePaymentPlans, getSignInUrlForPricing } from "@/lib/payment-utils";
import { useState } from "react";
import { clientEnv } from "@/env/client";

export default function PricingSection() {
  const { data: session } = authClient.useSession();
  const { billingState } = useUserBillingStatus({ enabled: !!session });
  const plans = getActivePaymentPlans();
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  
  // Find the monthly plans
  const proMonthlyPlan = plans.find(plan => plan.slug === "pro-monthly");
  const unlimitedMonthlyPlan = plans.find(plan => plan.slug === "unlimited-monthly");
  
  // Determine if user has used trial before
  const hasUsedTrial = billingState?.activeSubscriptions?.some(sub => 
    sub.status === 'trialing' || sub.status === 'active' || sub.status === 'canceled'
  ) || false;

  const getButtonText = () => {
    if (!session) {
      return "Start free trial";
    }
    
    if (hasUsedTrial) {
      return "Get started";
    }
    
    return "Start free trial";
  };

  const handlePlanClick = async (planSlug: string | undefined, planName: string) => {
    if (!planSlug) return;
    
    setLoadingPlan(planName);
    
    try {
      // For non-logged users, redirect to signup
      if (!session) {
        window.location.href = getSignInUrlForPricing();
        return;
      }

      // Check if Polar is enabled
      if (!clientEnv.NEXT_PUBLIC_ENABLE_POLAR) {
        alert("Development Mode: Polar payments are disabled. You can test the subscription features with mock data. Check the console for more details.");
        console.log("Polar disabled - using mock data for development");
        setLoadingPlan(null);
        return;
      }

      // Use the Polar checkout URL directly
      const checkoutUrl = `/api/auth/checkout/${planSlug}`;
      window.location.href = checkoutUrl;
      
    } catch (error) {
      console.error("Error starting checkout:", error);
      
      // Show user-friendly error message
      alert("Sorry, there was an issue starting your trial. Please check the console for more details or contact support if the problem persists.");
      
      setLoadingPlan(null);
    }
  };

  const planConfigs = [
    {
      name: "Pro",
      price: 9,
      badge: "Most Popular",
      features: [
        "50 route optimizations per month",
        "Up to 55 stops per route",
        "500 image uploads per month",
        "AI-powered address extraction"
      ],
      buttonText: getButtonText(),
      buttonVariant: "default" as const,
      cardClassName: "border-blue-200 dark:border-blue-800 relative",
      planSlug: proMonthlyPlan?.slug,
    },
    {
      name: "Unlimited",
      price: 29,
      badge: null,
      features: [
        "Unlimited route optimizations",
        "Up to 100 stops per route",
        "Unlimited image uploads",
        "AI-powered address extraction"
      ],
      buttonText: getButtonText(),
      buttonVariant: "outline" as const,
      cardClassName: "border-border",
      planSlug: unlimitedMonthlyPlan?.slug,
    },
  ];

  return (
    <section id="pricing" className="py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Choose the plan that fits your needs best.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {planConfigs.map((plan) => (
            <Card key={plan.name} className={plan.cardClassName}>
              {plan.badge && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge color="blue" className="bg-blue-600 text-white px-3 py-1">
                    {plan.badge}
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-bold mb-2">
                  {plan.name}
                </CardTitle>
                
                <div className="flex items-baseline justify-center gap-1">
                  <span className="text-4xl font-bold">${plan.price}</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button 
                  variant={plan.buttonVariant}
                  className="w-full gap-2"
                  size="lg"
                  onClick={() => handlePlanClick(plan.planSlug, plan.name)}
                  disabled={loadingPlan === plan.name}
                >
                  {plan.name === "Pro" && <Crown className="h-4 w-4" />}
                  {loadingPlan === plan.name ? "Loading..." : plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-sm text-muted-foreground">
            All plans include a 7-day free trial
          </p>
        </div>
      </div>
    </section>
  );
} 