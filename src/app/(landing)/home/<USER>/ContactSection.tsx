"use client";

import { motion } from "framer-motion";
import { Mail, Phone, MapPin, Clock, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/enhanced-button";
import { Card, CardContent } from "@/components/ui/card";

export default function ContactSection() {
  const contactInfo = [
    {
      icon: Mail,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>"
    },
    {
      icon: Phone,
      label: "Phone",
      value: "+****************",
      href: "tel:+15551234567"
    },
  ];

  return (
    <section className="py-20 lg:py-32 bg-muted/20">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold mb-4">
              Get in 
              <span className="text-muted-foreground">
                {" "}touch
              </span>
            </h2>
            <p className="text-xl text-muted-foreground">
              Ready to optimize your routes? Our team is here to help you get started.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div>
                <h3 className="text-2xl font-semibold mb-6">Contact Information</h3>
                <div className="space-y-6">
                  {contactInfo.map((item, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        <div className="inline-flex rounded-lg bg-muted/30 p-3">
                          <item.icon className="h-5 w-5 text-foreground" />
                        </div>
                      </div>
                      <div>
                        <p className="font-medium text-foreground">{item.label}</p>
                        {item.href && item.href !== "#" ? (
                          <a
                            href={item.href}
                            className="text-muted-foreground hover:text-foreground transition-colors"
                          >
                            {item.value}
                          </a>
                        ) : (
                          <p className="text-muted-foreground">{item.value}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 backdrop-blur-sm">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-semibold mb-6">Send us a message</h3>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">First Name</label>
                        <input
                          type="text"
                          className="w-full px-4 py-3 rounded-lg border border-input bg-background/50 focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring/20"
                          placeholder="John"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Last Name</label>
                        <input
                          type="text"
                          className="w-full px-4 py-3 rounded-lg border border-input bg-background/50 focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring/20"
                          placeholder="Doe"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Email</label>
                      <input
                        type="email"
                        className="w-full px-4 py-3 rounded-lg border border-input bg-background/50 focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring/20"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Company</label>
                      <input
                        type="text"
                        className="w-full px-4 py-3 rounded-lg border border-input bg-background/50 focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring/20"
                        placeholder="Your company name"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Message</label>
                      <textarea
                        rows={4}
                        className="w-full px-4 py-3 rounded-lg border border-input bg-background/50 focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring/20 resize-none"
                        placeholder="Tell us about your delivery challenges..."
                      ></textarea>
                    </div>
                    
                    <Button
                      type="submit"
                      className="w-full rounded-md"
                    >
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
} 