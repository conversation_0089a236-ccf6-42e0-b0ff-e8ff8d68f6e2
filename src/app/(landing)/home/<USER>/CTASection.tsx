"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Star } from "lucide-react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/enhanced-button";

export default function CTASection() {

  const trustIndicators = [
    { icon: Shield, text: "SOC 2 Compliant" },
    { icon: Star, text: "4.9/5 Customer Rating" },
    { icon: Zap, text: "AI text extraction" },
  ];

  return (
    <section className="py-20 lg:py-32 bg-muted/20">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-4xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center space-y-8"
          >
            {/* Main CTA Content */}
            <div className="space-y-6">
              <h2 className="text-4xl lg:text-6xl font-bold">
                Ready to optimize your 
                <span className="text-muted-foreground">
                  {" "}delivery routes?
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Join hundreds of delivery drivers already saving time and money with our intelligent route optimization.
              </p>
            </div>


            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button
                href="/signup"
                size="lg"
                leftIcon={ArrowRight}
                className="rounded-md text-lg px-8"
              >
                Start Optimizing Today
              </Button>
              
              <Button
                href="/signup"
                variant="outline"
                size="lg"
                className="rounded-md text-lg px-8"
              >
                Sign Up
              </Button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row items-center justify-center gap-6 pt-8 border-t"
            >
              {trustIndicators.map((indicator, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                  <indicator.icon className="h-4 w-4" />
                  <span>{indicator.text}</span>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
} 