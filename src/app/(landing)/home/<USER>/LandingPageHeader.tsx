"use client";

import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { MapPin, Menu, X, LayoutDashboard } from "lucide-react";
import { Button } from "@/components/ui/enhanced-button";
import { ThemeSwitchMinimalNextThemes } from "@/components/ThemeSwitchMinimalNextThemes";
import { APP_NAME } from "@/config/config";
import { useState } from "react";
import { useCurrentUser } from "@/hooks/useCurrentUser";

export default function LandingPageHeader() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  // Authentication disabled - always show dashboard button
  // const user = useCurrentUser();
  // const isLoggedIn = !!user;

  const navigationLinks = [
    { label: "Home", href: "/" },
    { label: "Features", href: "#features" },
    { label: "Price", href: "#pricing" },
    { label: "FAQ", href: "#faq" },
    { label: "Support", href: "#support" },
  ];

  return (
    <>
      <motion.header
        className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-xl border-b border-border/40"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            {/* Logo with Icon */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <MapPin className="h-6 w-6 text-foreground" />
                <span className="text-xl font-bold text-foreground">{APP_NAME}</span>
              </Link>
            </div>

            {/* Navigation Links - Center (Desktop only) */}
            <nav className="hidden md:flex items-center space-x-8">
              {navigationLinks.map((link, index) => (
                <motion.div
                  key={link.href}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link
                    href={link.href}
                    className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors duration-200"
                  >
                    {link.label}
                  </Link>
                </motion.div>
              ))}
            </nav>

            {/* Right Side */}
            <div className="flex items-center space-x-2">
              <ThemeSwitchMinimalNextThemes buttonProps={{ variant: "ghost", size: "sm" }} />

              {/* Desktop Dashboard Button */}
              <div className="hidden md:flex items-center space-x-2">
                <Button
                  href="/routes"
                  leftIcon={LayoutDashboard}
                  size="sm"
                >
                  Dashboard
                </Button>
              </div>

              {/* Mobile Menu Button */}
              <div className="md:hidden">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="p-2"
                  aria-label="Toggle mobile menu"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Full-Screen Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="fixed inset-0 z-[60] bg-background md:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {/* Header */}
            <div className="flex h-16 items-center justify-between px-6 border-b">
              <Link href="/" className="flex items-center space-x-2">
                <MapPin className="h-6 w-6 text-foreground" />
                <span className="text-xl font-bold text-foreground">{APP_NAME}</span>
              </Link>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(false)}
                className="p-2 rounded-md bg-muted/50"
                aria-label="Close mobile menu"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Navigation Content */}
            <div className="flex flex-col justify-between h-[calc(100vh-4rem)]">
              {/* Navigation Links */}
              <div className="px-6 pt-12">
                {navigationLinks.map((link, index) => (
                  <motion.div
                    key={link.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
                  >
                    <Link
                      href={link.href}
                      className="block py-4 text-xl font-medium text-foreground border-b border-dashed border-border last:border-b-0"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {link.label}
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* Bottom Section */}
              <motion.div
                className="px-6 pb-12 space-y-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <div className="flex justify-center">
                  <Button
                    href="/routes"
                    leftIcon={LayoutDashboard}
                    className="w-full max-w-xs h-12 rounded-lg font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
