"use client";

import { motion } from "framer-motion";
import { Star, Quote } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export default function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Fleet Manager",
      company: "Swift Deliveries",
      content: "OptiRoute Pro reduced our fuel costs by 35% and cut planning time from 2 hours to 15 minutes daily. The AI optimization is incredible.",
      rating: 5,
    },
    {
      name: "<PERSON>",
      role: "Operations Director", 
      company: "Metro Logistics",
      content: "We've seen a 40% improvement in delivery times and our drivers love the turn-by-turn navigation. Customer satisfaction is at an all-time high.",
      rating: 5,
    },
    {
      name: "<PERSON>",
      role: "CEO",
      company: "Urban Express",
      content: "The image recognition feature is a game-changer. Our team can now add stops by simply taking photos of receipts or addresses.",
      rating: 5,
    },
  ];

  return (
    <section className="py-20 lg:py-32 bg-muted/20">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-3xl text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl lg:text-5xl font-bold mb-4">
              Trusted by delivery teams worldwide
            </h2>
            <p className="text-xl text-muted-foreground">
              See how companies are transforming their operations with intelligent route optimization.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-0 bg-background/60 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {Array.from({ length: testimonial.rating }).map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  
                  <Quote className="h-8 w-8 text-muted-foreground mb-4" />
                  
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {testimonial.content}
                  </p>
                  
                  <div className="border-t pt-4">
                    <p className="font-semibold">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {testimonial.role}, {testimonial.company}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
} 