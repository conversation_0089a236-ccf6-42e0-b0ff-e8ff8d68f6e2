import { NextRequest, NextResponse } from "next/server";
import { clientEnv } from "@/env/client";

export async function POST(request: NextRequest) {
  try {
    // Check if Polar is enabled
    if (!clientEnv.NEXT_PUBLIC_ENABLE_POLAR) {
      return NextResponse.json(
        { message: "Polar webhooks are disabled in development mode" },
        { status: 200 }
      );
    }

    // Get the webhook payload
    const payload = await request.json();
    
    // Log the webhook event for debugging
    console.log("Polar webhook received:", {
      type: payload.type,
      timestamp: new Date().toISOString(),
      data: payload.data
    });

    // Handle different webhook events
    switch (payload.type) {
      case "subscription.created":
        console.log("New subscription created:", payload.data);
        break;
      
      case "subscription.updated":
        console.log("Subscription updated:", payload.data);
        break;
      
      case "subscription.cancelled":
        console.log("Subscription cancelled:", payload.data);
        break;
      
      case "payment.succeeded":
        console.log("Payment succeeded:", payload.data);
        break;
      
      case "payment.failed":
        console.log("Payment failed:", payload.data);
        break;
      
      default:
        console.log("Unhandled webhook event:", payload.type);
    }

    // Return success response
    return NextResponse.json({ received: true }, { status: 200 });
    
  } catch (error) {
    console.error("Error processing Polar webhook:", error);
    return NextResponse.json(
      { error: "Failed to process webhook" },
      { status: 500 }
    );
  }
}

// Handle GET requests (for webhook verification)
export async function GET() {
  return NextResponse.json(
    { message: "Polar webhooks endpoint is active" },
    { status: 200 }
  );
}

// Handle all other HTTP methods with proper 405 response
export async function PUT() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405, headers: { Allow: "POST, GET" } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405, headers: { Allow: "POST, GET" } }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405, headers: { Allow: "POST, GET" } }
  );
}

export async function HEAD() {
  return new NextResponse(null, {
    status: 405,
    headers: { Allow: "POST, GET" }
  });
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      Allow: "POST, GET",
      "Access-Control-Allow-Methods": "POST, GET",
      "Access-Control-Allow-Headers": "Content-Type, Authorization"
    }
  });
} 