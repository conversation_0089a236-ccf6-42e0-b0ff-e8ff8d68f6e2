import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/server/auth";
import { headers } from "next/headers";
import { serverEnv } from "@/env";
import { createTRPCContext, createCallerFactory } from "@/server/api/trpc";
import { appRouter } from "@/server/api/root";

export async function POST(request: NextRequest) {
  try {
    // Check if Polar is enabled
    if (!serverEnv.NEXT_PUBLIC_ENABLE_POLAR) {
      return NextResponse.json(
        { error: "Polar is not enabled" },
        { status: 400 }
      );
    }

    // Get the session
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    const { planSlug } = await request.json();

    if (!planSlug) {
      return NextResponse.json(
        { error: "Plan slug is required" },
        { status: 400 }
      );
    }

    // Map plan slugs to product IDs
    const productIdMap: Record<string, string | undefined> = {
      'pro': serverEnv.NEXT_PUBLIC_POLAR_ENV === "sandbox"
        ? serverEnv.POLAR_PRO_PRODUCT_ID_SANDBOX
        : serverEnv.POLAR_PRO_PRODUCT_ID_PROD,
      'unlimited': serverEnv.NEXT_PUBLIC_POLAR_ENV === "sandbox"
        ? serverEnv.POLAR_UNLIMITED_PRODUCT_ID_SANDBOX
        : serverEnv.POLAR_UNLIMITED_PRODUCT_ID_PROD,
    };

    console.log(`Upgrade request: planSlug=${planSlug}, environment=${serverEnv.NEXT_PUBLIC_POLAR_ENV}`);

    const newProductId = productIdMap[planSlug];
    if (!newProductId) {
      const envSuffix = serverEnv.NEXT_PUBLIC_POLAR_ENV.toUpperCase();
      console.error(`No product ID found for plan slug: ${planSlug}`);
      return NextResponse.json(
        { 
          error: `Product ID not configured for plan: ${planSlug} in ${serverEnv.NEXT_PUBLIC_POLAR_ENV} environment. Please set POLAR_${planSlug.toUpperCase()}_PRODUCT_ID_${envSuffix} in your environment variables.` 
        },
        { status: 400 }
      );
    }

    console.log(`Using product ID: ${newProductId} for plan: ${planSlug}`);

    // Get current subscription to upgrade
    const ctx = await createTRPCContext({ headers: await headers() });
    const createCaller = createCallerFactory(appRouter);
    const caller = createCaller(ctx);
    
    console.log(`Getting billing state for user: ${session.user.email}`);
    const polarState = await caller.polar.getBillingState();
    console.log(`Polar state:`, JSON.stringify(polarState, null, 2));
    
    if (!polarState.activeSubscriptions || polarState.activeSubscriptions.length === 0) {
      console.error("No active subscription found");
      return NextResponse.json(
        { error: "No active subscription found to upgrade" },
        { status: 400 }
      );
    }

    const currentSubscription = polarState.activeSubscriptions[0];
    if (!currentSubscription) {
      console.error("Current subscription is null or undefined");
      return NextResponse.json(
        { error: "No valid subscription found to upgrade" },
        { status: 400 }
      );
    }
    
    console.log(`Current subscription:`, JSON.stringify(currentSubscription, null, 2));

    // Check if this is a mock subscription (dev-user-fallback indicates mock data)
    if (currentSubscription.id === 'dev-user-fallback' || currentSubscription.id?.includes('mock')) {
      console.log("Detected mock subscription, simulating upgrade...");
      
      // Simulate successful upgrade for development
      const newAmount = planSlug === 'unlimited' ? 2900 : 900;
      
      // Update our in-memory subscription store with the new amount
      await caller.polar.updateSubscriptionState({
        email: session.user.email,
        amount: newAmount,
      });

      console.log(`Mock upgrade successful: ${planSlug} -> $${newAmount/100}`);
      
      return NextResponse.json({
        success: true,
        subscription: {
          ...currentSubscription,
          amount: newAmount,
          product_id: newProductId
        },
        message: "Subscription upgraded successfully (development mode)"
      });
    }

    // Use Polar's subscription update API to change the product
    const polarApiUrl = serverEnv.NEXT_PUBLIC_POLAR_ENV === "sandbox"
      ? 'https://sandbox-api.polar.sh' 
      : 'https://api.polar.sh';

    const accessToken = serverEnv.NEXT_PUBLIC_POLAR_ENV === "sandbox"
      ? serverEnv.POLAR_ACCESS_TOKEN_SANDBOX
      : serverEnv.POLAR_ACCESS_TOKEN_PROD;

    console.log(`Updating subscription ${currentSubscription.id} to product ${newProductId}`);

    const updateResponse = await fetch(`${polarApiUrl}/v1/subscriptions/${currentSubscription.id}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        product_id: newProductId,
      }),
    });

    if (!updateResponse.ok) {
      const errorData = await updateResponse.text();
      console.error('Polar subscription update failed:', errorData);
      return NextResponse.json(
        { error: "Failed to update subscription" },
        { status: 500 }
      );
    }

    const updatedSubscription = await updateResponse.json();
    console.log('Subscription updated successfully:', updatedSubscription);

    // Update our in-memory subscription store with the new amount
    const newAmount = updatedSubscription.amount || (planSlug === 'unlimited' ? 2900 : 900);
    await caller.polar.updateSubscriptionState({
      email: session.user.email,
      amount: newAmount,
    });

    return NextResponse.json({
      success: true,
      subscription: updatedSubscription,
      message: "Subscription upgraded successfully"
    });

  } catch (error) {
    console.error("Subscription upgrade error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 