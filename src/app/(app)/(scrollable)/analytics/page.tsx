"use client";

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Clock,
  Fuel,
  Route,
  MapPin,
  DollarSign,
  Calendar,
} from "lucide-react";
import { useUserAnalytics } from "@/hooks/useUserAnalytics";
import { Spinner } from "@/components/Spinner";

export default function AnalyticsPage() {
  const { analytics, isLoading, error } = useUserAnalytics();

  if (isLoading) {
    return (
      <div className="container mx-auto p-0 flex items-center justify-center min-h-[400px]">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-0 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-muted-foreground">Failed to load analytics data</p>
          <p className="text-sm text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="container mx-auto p-0 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-muted-foreground">No analytics data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-0 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Track your route optimization performance and savings
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Routes</CardTitle>
            <Route className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalRoutes}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{analytics.routesThisMonth}</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Distance Saved</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalDistanceSaved} miles</div>
            <p className="text-xs text-muted-foreground">
              Out of {Math.round(analytics.totalDistanceSaved * 3.2)} miles total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalTimeSaved}h</div>
            <p className="text-xs text-muted-foreground">
              Average {analytics.totalRoutes > 0 ? (analytics.totalTimeSaved / analytics.totalRoutes).toFixed(1) : 0}h per route
            </p>
          </CardContent>
        </Card>

      </div>

      {/* Performance Overview */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Optimization Efficiency */}
        <Card>
          <CardHeader>
            <CardTitle>Optimization Efficiency</CardTitle>
            <CardDescription>
              Overall performance metrics for route optimization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Average Optimization</span>
                <span className="font-medium">{analytics.averageOptimization}%</span>
              </div>
              <Progress value={analytics.averageOptimization} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Route Efficiency</span>
                <span className="font-medium">{analytics.routeEfficiency}%</span>
              </div>
              <Progress value={analytics.routeEfficiency} className="h-2" />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{analytics.totalDistanceSaved}</div>
                <div className="text-xs text-muted-foreground">Miles Saved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{analytics.totalTimeSaved}</div>
                <div className="text-xs text-muted-foreground">Hours Saved</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Avg stops per route</span>
                <span className="font-medium">{analytics.averageStopsPerRoute}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Best optimization</span>
                <span className="font-medium">{analytics.bestOptimization}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Success rate</span>
                <span className="font-medium">{analytics.successRate}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Insights */}
      <div className="grid gap-6 lg:grid-cols-3">

       
      </div>
    </div>
  );
} 