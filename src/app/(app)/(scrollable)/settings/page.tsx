"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/enhanced-button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { 
  Settings, 
  MapPin, 
  CreditCard, 
  Globe, 
  Palette, 
  Crown,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Smartphone,
  Monitor,
  RefreshCw
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SegmentedControl } from "@/components/SegmentedControl";
import { useState, useEffect } from "react";
import { useUserAnalytics } from "@/hooks/useUserAnalytics";
import { useSubscriptionLimits } from "@/hooks/useSubscriptionLimits";
import { Spinner } from "@/components/Spinner";
import { useUserBillingStatus } from "@/hooks/useUserBillingStatus";
import { useDialog } from "@/components/DialogManager";
import { ChangePlanDialog } from "@/components/settings/ChangePlanDialog";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";

export default function SettingsPage() {
  const [navigationApp, setNavigationApp] = useState("GOOGLE_MAPS");
  const [language, setLanguage] = useState("en");
  const [theme, setTheme] = useState("system");
  
  const searchParams = useSearchParams();
  const { analytics, isLoading: analyticsLoading } = useUserAnalytics();
  const subscriptionLimits = useSubscriptionLimits();
  const { billingState, isLoading: billingLoading, refresh, forceRefresh } = useUserBillingStatus({ enabled: true });
  const { openDialog } = useDialog();

  // Load preferences from localStorage on mount
  useEffect(() => {
    const savedNavigationApp = localStorage.getItem('navigationApp');
    if (savedNavigationApp) {
      setNavigationApp(savedNavigationApp);
    }
    
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
      setLanguage(savedLanguage);
    }
    
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  // Auto-refresh billing data when returning from checkout
  useEffect(() => {
    const checkoutSuccess = searchParams.get('checkout');
    const upgradeSuccess = searchParams.get('upgrade');
    
    if (checkoutSuccess === 'success') {
      toast.success("Plan updated successfully! Refreshing billing information...");
      // Delay refresh to allow webhook processing
      const timer = setTimeout(() => {
        refresh();
      }, 2000);
      
      return () => clearTimeout(timer);
    }
    
    if (upgradeSuccess === 'success') {
      toast.success("Plan upgraded successfully! Refreshing billing information...");
      // Delay refresh to allow webhook processing
      const timer = setTimeout(() => {
        refresh();
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [searchParams, refresh]);

  // Save navigation app preference when changed
  const handleNavigationAppChange = (value: string) => {
    setNavigationApp(value);
    localStorage.setItem('navigationApp', value);
  };

  // Save language preference when changed
  const handleLanguageChange = (value: string) => {
    setLanguage(value);
    localStorage.setItem('language', value);
  };

  // Save theme preference when changed
  const handleThemeChange = (value: string) => {
    setTheme(value);
    localStorage.setItem('theme', value);
  };

  // Get plan information from billing state
  const activeSubscription = billingState?.activeSubscriptions?.[0];
  
  // Determine actual plan name based on subscription
  let planName = 'Trial';
  if (subscriptionLimits.planTier === 'PRO') {
    planName = 'Pro Plan';
  } else if (subscriptionLimits.planTier === 'UNLIMITED') {
    planName = 'Unlimited Plan';
  }
  
  const billingCycle = activeSubscription?.recurringInterval === 'month' ? 'Monthly' : 
                       activeSubscription?.recurringInterval === 'year' ? 'Annual' : 'Trial';
  
  // Calculate usage percentage for current plan
  const usagePercentage = subscriptionLimits.usage && subscriptionLimits.planLimits.monthlyRouteLimit !== Infinity
    ? (subscriptionLimits.usage.routesOptimizedThisMonth / subscriptionLimits.planLimits.monthlyRouteLimit) * 100
    : 0;

  const remainingRoutes = subscriptionLimits.usage && subscriptionLimits.planLimits.monthlyRouteLimit !== Infinity
    ? subscriptionLimits.planLimits.monthlyRouteLimit - subscriptionLimits.usage.routesOptimizedThisMonth
    : Infinity;

  // Calculate image upload usage (mock for now - would come from real usage tracking)
  const imageUploadsUsed = 0; // This would come from actual usage tracking
  const imageUploadLimit = subscriptionLimits.planLimits.maxImagesPerUpload;
  const imageUsagePercentage = imageUploadLimit !== Infinity 
    ? (imageUploadsUsed / imageUploadLimit) * 100 
    : 0;

  // Format next billing date
  const nextBillingDate = activeSubscription?.currentPeriodEnd 
    ? new Date(activeSubscription.currentPeriodEnd).toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    : 'N/A';

  // Format amount
  const amount = activeSubscription?.amount 
    ? `$${(activeSubscription.amount / 100).toFixed(2)}`
    : 'Free during trial';

  const handleChangePlan = () => {
    openDialog({
      component: ChangePlanDialog,
      size: "xl",
      showCloseButton: true,
      mobileView: "bottom-drawer",
      drawerTitle: "Change Your Plan",
      classNames: {
        body: "max-h-[70vh] overflow-y-auto",
        drawerContent: "max-h-[85vh] overflow-y-auto"
      }
    });
  };

  const handleRefreshBilling = async () => {
    try {
      toast.info("Refreshing billing information...");
      await forceRefresh();
      toast.success("Billing information updated!");
    } catch (error) {
      console.error("Failed to refresh billing:", error);
      toast.error("Failed to refresh billing information");
    }
  };

  const handleDebugBilling = () => {
    console.log("Current billing state:", billingState);
    console.log("Subscription limits:", subscriptionLimits);
    toast.info("Billing debug info logged to console");
  };

  return (
    <div className="container mx-auto p-0 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account, billing, and preferences
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleDebugBilling}
          >
            Debug
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefreshBilling}
            disabled={billingLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${billingLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs defaultValue="usage" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="usage">Usage & Plan</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        {/* Usage & Plan Tab */}
        <TabsContent value="usage" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Current Plan */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-5 w-5 text-blue-600" />
                  Current Plan
                </CardTitle>
                <CardDescription>
                  Your active subscription and benefits
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{planName}</span>
                  <Badge color="blue" variant="outline">{billingCycle}</Badge>
                </div>
                
                {/* Route Optimizations Usage */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Route Optimizations</span>
                    <span className="font-medium">
                      {subscriptionLimits.usage?.routesOptimizedThisMonth || 0} / {subscriptionLimits.planLimits.monthlyRouteLimit === Infinity ? '∞' : subscriptionLimits.planLimits.monthlyRouteLimit}
                    </span>
                  </div>
                  <Progress value={usagePercentage} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {remainingRoutes === Infinity ? 'Unlimited' : `${remainingRoutes} optimizations remaining this month`}
                  </p>
                </div>

                {/* Image Uploads Usage */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Image Uploads</span>
                    <span className="font-medium">
                      {imageUploadsUsed} / {imageUploadLimit === Infinity ? '∞' : imageUploadLimit}
                    </span>
                  </div>
                  <Progress value={imageUsagePercentage} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {imageUploadLimit === Infinity ? 'Unlimited uploads' : `${imageUploadLimit - imageUploadsUsed} uploads remaining`}
                  </p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Next billing</span>
                    <span className="font-medium">{nextBillingDate}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Amount</span>
                    <span className="font-medium">{amount}</span>
                  </div>
                </div>

                <Button variant="outline" className="w-full" onClick={handleChangePlan}>
                  Change Plan
                </Button>
              </CardContent>
            </Card>

            {/* Usage Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>This Month's Usage</CardTitle>
                <CardDescription>
                  Detailed breakdown of your activity
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {analyticsLoading ? (
                  <div className="flex justify-center py-8">
                    <Spinner />
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {analytics?.routesThisMonth || 0}
                        </div>
                        <div className="text-xs text-muted-foreground">Routes Optimized</div>
                      </div>
                      <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {analytics?.totalDistanceSaved || 0}
                        </div>
                        <div className="text-xs text-muted-foreground">Miles Saved</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Route optimization</span>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium">Active</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Image recognition</span>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium">Active</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Analytics dashboard</span>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium">Active</span>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Billing Tab */}
        <TabsContent value="billing" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Payment Method */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Method
                </CardTitle>
                <CardDescription>
                  Manage your payment information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">No payment method on file</p>
                  <Button variant="outline">
                    Add Payment Method
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Billing History */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Invoices</CardTitle>
                <CardDescription>
                  Your billing history and invoices
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-8">
                  <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                    <CreditCard className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <p className="text-muted-foreground mb-2">No invoices yet</p>
                  <p className="text-sm text-muted-foreground">
                    Your billing history will appear here once you have active subscriptions
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Navigation Preferences */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Navigation
                </CardTitle>
                <CardDescription>
                  Choose your preferred navigation app
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Default Navigation App</Label>
                    <SegmentedControl
                      value={navigationApp}
                      onChange={handleNavigationAppChange}
                      options={[
                        { value: "GOOGLE_MAPS", label: "Google Maps" },
                        { value: "APPLE_MAPS", label: "Apple Maps" },
                      ]}
                    />
                  </div>
                </div>

                <div className="p-4 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Smartphone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Mobile Integration</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Routes will automatically open in your selected navigation app when accessed from mobile devices.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Appearance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Appearance
                </CardTitle>
                <CardDescription>
                  Customize your interface preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Theme</Label>
                    <SegmentedControl
                      value={theme}
                      onChange={handleThemeChange}
                      options={[
                        { value: "light", label: "Light" },
                        { value: "dark", label: "Dark" },
                        { value: "system", label: "System" },
                      ]}
                    />
                  </div>
                </div>

                <div className="p-4 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Monitor className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">System Sync</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Theme automatically adjusts based on your system preferences when set to auto.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Language */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Language
                </CardTitle>
                <CardDescription>
                  Select your preferred language
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Interface Language</Label>
                    <SegmentedControl
                      value={language}
                      onChange={handleLanguageChange}
                      options={[
                        { value: "en", label: "English" },
                        { value: "ru", label: "Русский" },
                      ]}
                    />
                  </div>
                </div>

                <div className="p-4 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <span className="text-sm font-medium">Translation Status</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Russian translation is currently in beta. Some interface elements may still appear in English.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

    
      </Tabs>
    </div>
  );
} 