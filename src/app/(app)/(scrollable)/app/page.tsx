"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Users,
  CreditCard,
  ArrowUpRight,
  Clock,
  Plus,
  Settings,
  Bell,
  Search,
} from "lucide-react";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

export default function AppPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to routes page since it's now the main dashboard
    router.replace("/routes");
  }, [router]);

  // Show loading while redirecting
  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-foreground mx-auto mb-4"></div>
          <p className="text-muted-foreground">Redirecting to dashboard...</p>
        </div>
      </div>
    </div>
  );
}
