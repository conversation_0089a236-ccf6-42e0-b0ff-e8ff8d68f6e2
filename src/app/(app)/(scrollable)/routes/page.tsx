"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/enhanced-button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  MapPin,
  Route,
  Clock,
  Plus,
  Upload,
  CheckCircle,
  TrendingUp,
  Camera,
  X,
  Navigation,
  Undo,
  Check,
  Map,
  Crosshair,
  Loader2,
  GripVertical,
  Crown,
  AlertTriangle,
} from "lucide-react";
import { optimizeRoute, parseAddressesFromImage, type RouteOptimizationRequest, type OptimizedRoute, type RouteStop } from "@/lib/route-optimization";
import { NavigationService } from "@/lib/navigationService";
import { getCurrentLocation, reverseGeocode } from "@/lib/geocoding";
import { RouteMap } from "@/components/RouteMap";
import { useSubscriptionLimits } from "@/hooks/useSubscriptionLimits";
import { SubscriptionLimitDialog } from "@/components/SubscriptionLimitDialog";
import { type LimitCheckResult } from "@/lib/subscription-limits";
import { useRecentRoutes } from "@/hooks/useUserAnalytics";
import { Spinner } from "@/components/Spinner";

// Local storage keys
const STORAGE_KEYS = {
  OPTIMIZED_ROUTE: 'optiroute-optimized-route',
  ACTIVE_TAB: 'optiroute-active-tab',
  FORM_DATA: 'optiroute-form-data'
};

// Type for form data persistence
interface FormData {
  startingAddress: string;
  destinationAddress: string;
  stops: string[];
}

export default function RoutesPage() {
  const [activeTab, setActiveTab] = useState("create");
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [stops, setStops] = useState([""]); // Dynamic stops array
  const [startingAddress, setStartingAddress] = useState("");
  const [destinationAddress, setDestinationAddress] = useState("");
  const [optimizedRoute, setOptimizedRoute] = useState<OptimizedRoute | null>(null);
  const [userNavigationApp, setUserNavigationApp] = useState<'GOOGLE_MAPS' | 'APPLE_MAPS'>('GOOGLE_MAPS');
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [imageProcessingStatus, setImageProcessingStatus] = useState<string>("");
  
  // Subscription limits integration
  const subscriptionLimits = useSubscriptionLimits();
  const [limitDialog, setLimitDialog] = useState<{
    isOpen: boolean;
    limitResult: LimitCheckResult | null;
    feature?: string;
  }>({
    isOpen: false,
    limitResult: null,
  });

  // Get real user route data
  const { routes: recentRoutes, isLoading: routesLoading, error: routesError } = useRecentRoutes(10);

  // Load user preferences and persisted state on mount
  useEffect(() => {
    // Load navigation preference from localStorage or settings
    const savedNavigationApp = localStorage.getItem('navigationApp') as 'GOOGLE_MAPS' | 'APPLE_MAPS';
    if (savedNavigationApp) {
      setUserNavigationApp(savedNavigationApp);
      // Set preference in NavigationService
      NavigationService.setPreferences({ navigationApp: savedNavigationApp });
    } else {
      // Auto-detect best navigation app for platform
      const detectedApp = NavigationService.detectBestNavigationApp();
      setUserNavigationApp(detectedApp);
      NavigationService.setPreferences({ navigationApp: detectedApp });
    }

    // Load persisted optimized route
    const savedOptimizedRoute = localStorage.getItem(STORAGE_KEYS.OPTIMIZED_ROUTE);
    if (savedOptimizedRoute) {
      try {
        const parsedRoute = JSON.parse(savedOptimizedRoute) as OptimizedRoute;
        setOptimizedRoute(parsedRoute);
      } catch (error) {
        console.error('Failed to parse saved route:', error);
        localStorage.removeItem(STORAGE_KEYS.OPTIMIZED_ROUTE);
      }
    }

    // Load persisted active tab
    const savedActiveTab = localStorage.getItem(STORAGE_KEYS.ACTIVE_TAB);
    if (savedActiveTab && !savedOptimizedRoute) {
      setActiveTab(savedActiveTab);
    }

    // Load persisted form data
    const savedFormData = localStorage.getItem(STORAGE_KEYS.FORM_DATA);
    if (savedFormData) {
      try {
        const formData = JSON.parse(savedFormData) as FormData;
        setStartingAddress(formData.startingAddress || "");
        setDestinationAddress(formData.destinationAddress || "");
        setStops(formData.stops.length > 0 ? formData.stops : [""]);
      } catch (error) {
        console.error('Failed to parse saved form data:', error);
        localStorage.removeItem(STORAGE_KEYS.FORM_DATA);
      }
    }
  }, []);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    const formData: FormData = {
      startingAddress,
      destinationAddress,
      stops
    };
    localStorage.setItem(STORAGE_KEYS.FORM_DATA, JSON.stringify(formData));
  }, [startingAddress, destinationAddress, stops]);

  // Save active tab to localStorage
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.ACTIVE_TAB, activeTab);
  }, [activeTab]);

  // Save optimized route to localStorage whenever it changes
  useEffect(() => {
    if (optimizedRoute) {
      localStorage.setItem(STORAGE_KEYS.OPTIMIZED_ROUTE, JSON.stringify(optimizedRoute));
    }
  }, [optimizedRoute]);

  const addStop = () => {
    setStops([...stops, ""]);
  };

  const removeStop = (index: number) => {
    if (stops.length > 1) {
      setStops(stops.filter((_, i) => i !== index));
    }
  };

  const updateStop = (index: number, value: string) => {
    const newStops = [...stops];
    newStops[index] = value;
    setStops(newStops);
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    setDragOverIndex(null);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', '');
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    setDragOverIndex(null);
    
    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      return;
    }

    const newStops = [...stops];
    const draggedStop = newStops[draggedIndex];
    
    if (draggedStop === undefined) {
      setDraggedIndex(null);
      return;
    }
    
    // Remove dragged item
    newStops.splice(draggedIndex, 1);
    
    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newStops.splice(insertIndex, 0, draggedStop);
    
    setStops(newStops);
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  const handleUseCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      // Get current coordinates
      const location = await getCurrentLocation();
      
      // Reverse geocode to get address
      const address = await reverseGeocode(location.latitude, location.longitude);
      
      if (address) {
        setStartingAddress(address);
      } else {
        // Fallback to coordinates if reverse geocoding fails
        setStartingAddress(`${location.latitude}, ${location.longitude}`);
      }
    } catch (error) {
      console.error('Failed to get current location:', error);
      // TODO: Show error message to user
    } finally {
      setIsGettingLocation(false);
    }
  };

  const handleOptimizeRoute = async () => {
    if (!startingAddress || !destinationAddress) return;
    
    // Check subscription limits before optimization
    const totalStops = stops.filter(stop => stop.trim() !== "").length + 2; // +2 for start and destination
    const limitCheck = subscriptionLimits.checkRouteOptimization(totalStops);
    
    if (!limitCheck.allowed) {
      setLimitDialog({
        isOpen: true,
        limitResult: limitCheck,
        feature: "Route Optimization",
      });
      return;
    }
    
    setIsOptimizing(true);
    setOptimizationProgress(0);
    
    try {
      const routeData: RouteOptimizationRequest = {
        startingAddress,
        stops: stops.filter(stop => stop.trim() !== ""),
        destinationAddress
      };

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setOptimizationProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 15;
        });
      }, 300);

      // Call the actual optimization API
      const optimizedRouteResult = await optimizeRoute(routeData);
      
      clearInterval(progressInterval);
      setOptimizationProgress(100);
      
      // Store the optimized route for display
      setOptimizedRoute(optimizedRouteResult);
      
      console.log("Optimized route:", optimizedRouteResult);
      
      setTimeout(() => {
        setIsOptimizing(false);
        setOptimizationProgress(0);
        // No need to switch tabs since we're using the same tab
      }, 1000);

    } catch (error) {
      console.error("Route optimization failed:", error);
      setIsOptimizing(false);
      setOptimizationProgress(0);
      // TODO: Show error message to user
    }
  };

  const handleImageUpload = async (files: FileList) => {
    // Check subscription limits for image upload
    const limitCheck = subscriptionLimits.checkImageUpload(files.length);
    
    if (!limitCheck.allowed) {
      setLimitDialog({
        isOpen: true,
        limitResult: limitCheck,
        feature: "Image Upload",
      });
      return;
    }
    
    setIsProcessingImage(true);
    setImageProcessingStatus(`Processing ${files.length} image${files.length === 1 ? '' : 's'}...`);
    
    try {
      console.log(`Processing ${files.length} image(s) for address extraction...`);
      
      const allExtractedAddresses: string[] = [];
      let processedCount = 0;
      let totalAddressesFound = 0;
      const totalFiles = files.length; // Store the total count
      
      // Process all images
      for (const file of Array.from(files)) {
        try {
          setImageProcessingStatus(`Processing ${file.name} (${processedCount + 1}/${totalFiles})...`);
          console.log(`Processing image: ${file.name}`);
          
          // Parse addresses from each image
          const extractedAddresses = await parseAddressesFromImage(file);
          
          if (extractedAddresses.length > 0) {
            const filteredAddresses = extractedAddresses.filter(addr => addr && addr.trim() !== "");
            allExtractedAddresses.push(...filteredAddresses);
            totalAddressesFound += filteredAddresses.length;
            
            console.log(`Extracted ${filteredAddresses.length} addresses from ${file.name}:`, filteredAddresses);
          } else {
            console.log(`No addresses found in ${file.name}`);
          }
          
          processedCount++;
        } catch (error) {
          console.error(`Failed to process image ${file.name}:`, error);
          processedCount++;
          // Continue processing other images even if one fails
        }
      }
      
      setImageProcessingStatus("Combining results...");
      
      // Combine with existing stops and remove duplicates
      const existingStops = stops.filter(stop => stop.trim() !== "");
      const combinedAddresses = [...existingStops, ...allExtractedAddresses];
      
      // Remove duplicates while preserving order
      const uniqueAddresses = combinedAddresses.filter((address, index) => 
        combinedAddresses.findIndex(addr => 
          addr.toLowerCase().trim() === address.toLowerCase().trim()
        ) === index
      );
      
      // Update stops with combined results
      if (allExtractedAddresses.length > 0) {
        setStops(uniqueAddresses);
        
        // Show success message with details
        const message = totalFiles === 1 
          ? `✅ Successfully extracted ${totalAddressesFound} address${totalAddressesFound === 1 ? '' : 'es'} from the image and added as stops!`
          : `✅ Successfully processed ${processedCount} image${processedCount === 1 ? '' : 's'} and extracted ${totalAddressesFound} total address${totalAddressesFound === 1 ? '' : 'es'}!`;
        
        alert(message);
      } else {
        alert(`⚠️ No addresses were found in the ${totalFiles === 1 ? 'image' : `${totalFiles} images`}. Please try different images or enter addresses manually.`);
      }
      
    } catch (error) {
      console.error("Image processing failed:", error);
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      alert(`❌ ${errorMessage}`);
    } finally {
      setIsProcessingImage(false);
      setImageProcessingStatus("");
    }
  };

  const handleNavigateToStop = (stop: RouteStop) => {
    if (!stop.latitude || !stop.longitude) {
      console.error('Cannot navigate: No coordinates available for this stop');
      return;
    }
    
    const lat = stop.latitude;
    const lng = stop.longitude;
    const address = encodeURIComponent(stop.geocodedAddress || stop.address);
    
    let navigationURL: string;
    
    switch (userNavigationApp) {
      case 'GOOGLE_MAPS':
        // Google Maps URL scheme
        navigationURL = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=${address}`;
        break;
      case 'APPLE_MAPS':
        // Apple Maps URL scheme
        navigationURL = `http://maps.apple.com/?daddr=${lat},${lng}&dirflg=d`;
        break;
      default:
        navigationURL = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
    }
    
    // Open in new tab/window or try to open in app
    if (typeof window !== 'undefined') {
      window.open(navigationURL, '_blank');
    }
  };

  const handleCompleteStop = (stopIndex: number) => {
    if (!optimizedRoute) return;
    
    const updatedStops = [...optimizedRoute.optimizedStops];
    const currentStop = updatedStops[stopIndex];
    if (currentStop) {
      updatedStops[stopIndex] = {
        ...currentStop,
        isCompleted: true,
      };
      
      const updatedRoute = {
        ...optimizedRoute,
        optimizedStops: updatedStops,
      };
      
      setOptimizedRoute(updatedRoute);
    }
  };

  const handleUndoCompleteStop = (stopIndex: number) => {
    if (!optimizedRoute) return;
    
    const updatedStops = [...optimizedRoute.optimizedStops];
    const currentStop = updatedStops[stopIndex];
    if (currentStop) {
      updatedStops[stopIndex] = {
        ...currentStop,
        isCompleted: false,
      };
      
      const updatedRoute = {
        ...optimizedRoute,
        optimizedStops: updatedStops,
      };
      
      setOptimizedRoute(updatedRoute);
    }
  };

  const handleFinishAndStartNewRoute = () => {
    // Clear persisted state
    localStorage.removeItem(STORAGE_KEYS.OPTIMIZED_ROUTE);
    localStorage.removeItem(STORAGE_KEYS.FORM_DATA);
    localStorage.removeItem(STORAGE_KEYS.ACTIVE_TAB);
    
    // Reset component state
    setOptimizedRoute(null);
    setStartingAddress("");
    setDestinationAddress("");
    setStops([""]);
    setActiveTab("create");
    
    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge color="green">Completed</Badge>;
      case "in-progress":
        return <Badge color="blue">In Progress</Badge>;
      case "optimized":
        return <Badge color="purple">Optimized</Badge>;
      default:
        return <Badge color="gray" variant="outline">{status}</Badge>;
    }
  };

  // Helper function to format time from minutes to hours and minutes
  const formatTime = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return `${hours}h`;
    }
    return `${hours}h ${remainingMinutes}min`;
  };

  // Helper function to format distance to 1 decimal place
  const formatDistance = (distance: number): string => {
    return distance.toFixed(1);
  };

  return (
    <div className="container mx-auto p-0 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Route Optimization</h1>
          <p className="text-muted-foreground">
            Create, optimize, and manage your delivery routes efficiently
          </p>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="create">
            {optimizedRoute ? "Optimized Route" : "Create Route"}
          </TabsTrigger>
          <TabsTrigger value="routes">My Routes</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-4">
          {!optimizedRoute ? (
            /* Route Creation Form */
            <Card>
              <CardHeader>
                <CardTitle>Create New Route</CardTitle>
                <CardDescription>
                  Add addresses manually or upload images to extract addresses automatically
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Starting Address */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Starting Address *</Label>
                  <div className="relative">
                    <Input 
                      placeholder="Enter starting address..." 
                      value={startingAddress}
                      onChange={(e) => setStartingAddress(e.target.value)}
                      className="w-full pr-12"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleUseCurrentLocation}
                      disabled={isGettingLocation}
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                    >
                      {isGettingLocation ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Crosshair className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Click the crosshair icon to use your current location
                  </p>
                </div>

                {/* Stops Section */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Stops</Label>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        {stops.filter(s => s.trim()).length} / {subscriptionLimits.planLimits.maxStopsPerRoute === Infinity ? '∞' : subscriptionLimits.planLimits.maxStopsPerRoute} stops
                      </span>
                      {subscriptionLimits.isNearLimit(stops.filter(s => s.trim()).length + 2, subscriptionLimits.planLimits.maxStopsPerRoute, 0.8) && (
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    {stops.map((stop, index) => (
                      <div 
                        key={index} 
                        className={`flex items-center gap-2 transition-all duration-200 ${
                          draggedIndex === index ? 'opacity-50' : ''
                        } ${
                          dragOverIndex === index ? 'bg-muted/50 rounded-lg' : ''
                        }`}
                        draggable
                        onDragStart={(e) => handleDragStart(e, index)}
                        onDragOver={(e) => handleDragOver(e, index)}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDrop(e, index)}
                        onDragEnd={handleDragEnd}
                      >
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab active:cursor-grabbing flex-shrink-0" />
                          <Input
                            placeholder={`Stop ${index + 1}`}
                            value={stop}
                            onChange={(e) => updateStop(index, e.target.value)}
                            className="flex-1"
                          />
                        </div>
                        {stops.length > 1 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeStop(index)}
                            className="px-3 flex-shrink-0"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button 
                      variant="outline" 
                      leftIcon={Plus} 
                      onClick={addStop}
                      className="w-full"
                    >
                      Add Stop
                    </Button>
                  </div>
                </div>

                {/* Destination Address */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Destination Address *</Label>
                  <Input 
                    placeholder="Enter destination address..." 
                    value={destinationAddress}
                    onChange={(e) => setDestinationAddress(e.target.value)}
                    className="w-full"
                  />
                </div>

                <Separator />

                {/* Image Upload Alternative */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Or Upload Images</Label>
                    <span className="text-xs text-muted-foreground">
                      Max {subscriptionLimits.planLimits.maxImagesPerUpload} images • AI-powered
                    </span>
                  </div>
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                    <Camera className="mx-auto h-12 w-12 text-muted-foreground/50 mb-3" />
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        Upload images containing addresses (screenshots, photos, documents)
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Powered by AI for accurate address parsing
                      </p>
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={(e) => {
                        if (e.target.files) {
                          handleImageUpload(e.target.files);
                          // Reset the input so the same file can be selected again
                          e.target.value = '';
                        }
                      }}
                      className="hidden"
                      id="image-upload"
                      disabled={isProcessingImage}
                    />
                    <Button 
                      variant="outline" 
                      leftIcon={Upload} 
                      className="mt-4"
                      loading={isProcessingImage}
                      onClick={() => {
                        const fileInput = document.getElementById('image-upload') as HTMLInputElement;
                        fileInput?.click();
                      }}
                      disabled={isProcessingImage}
                    >
                      {isProcessingImage ? (imageProcessingStatus || "Processing...") : "Choose Images"}
                    </Button>
                  </div>
                </div>

                {isOptimizing && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Optimizing Route...</Label>
                      <span className="text-sm text-muted-foreground">{optimizationProgress}%</span>
                    </div>
                    <Progress value={optimizationProgress} className="w-full" />
                  </div>
                )}

                <Button 
                  onClick={handleOptimizeRoute} 
                  disabled={isOptimizing || !startingAddress || !destinationAddress}
                  loading={isOptimizing}
                  className="w-full"
                >
                  {isOptimizing ? "Optimizing..." : "Optimize Route"}
                </Button>
              </CardContent>
            </Card>
          ) : (
            /* Optimized Route Results */
            <div className="space-y-6">
              {/* Route Summary */}
              <Card>
                <CardHeader className="pb-2.5 sm:pb-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Optimized Route</CardTitle>
                    </div>
                    <Badge color="green" variant="outline">
                      {optimizedRoute.savings.percentage}% savings
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="py-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-xl font-bold">{formatDistance(optimizedRoute.totalDistance)} miles</div>
                      <div className="text-xs text-muted-foreground">
                        Total Distance
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold">{formatTime(optimizedRoute.totalDuration)}</div>
                      <div className="text-xs text-muted-foreground">
                        Total Duration
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold">{optimizedRoute.optimizedStops.length}</div>
                      <div className="text-xs text-muted-foreground">Total Stops</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Route Map */}
              <Card>
                <CardHeader>
                  <CardTitle>Route Map</CardTitle>
                  <CardDescription>
                    Interactive map showing your optimized route with numbered stops
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RouteMap 
                    stops={optimizedRoute.optimizedStops} 
                    polyline={optimizedRoute.polyline}
                    sections={optimizedRoute.sections}
                  />
                </CardContent>
              </Card>

              {/* Optimized Stops List */}
              <Card>
                <CardHeader>
                  <CardTitle>Route Stops</CardTitle>
                  <CardDescription>
                    Follow the optimized sequence for maximum efficiency
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {optimizedRoute.optimizedStops.map((stop, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${
                        stop.isCompleted 
                          ? 'border-green-500 dark:border-green-400' 
                          : 'border-border'
                      }`}
                    >
                      <div className="space-y-3">
                        {/* Address and Buttons Row */}
                        <div className="flex items-start justify-between gap-4">
                          <div className="min-w-0 flex-1">
                            <div className={`font-medium text-lg break-words ${stop.isCompleted ? 'line-through text-muted-foreground' : ''}`}>
                              {stop.address}
                            </div>
                            
                            {/* Number Label and Status */}
                            <div className="flex items-center gap-2 mt-2">
                              <span className="text-sm text-muted-foreground">
                                {index === 0 ? 'Starting Point' : index === optimizedRoute.optimizedStops.length - 1 ? 'Final Destination' : `Stop ${index}`}
                              </span>
                              
                              {stop.isCompleted && (
                                <Badge color="green" variant="outline" className="text-xs">Completed</Badge>
                              )}
                            </div>
                          </div>
                          
                          {/* Action Buttons */}
                          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 flex-shrink-0">
                            {stop.isCompleted ? (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleUndoCompleteStop(index)}
                                leftIcon={Undo}
                                className="w-full sm:w-auto"
                              >
                                Undo
                              </Button>
                            ) : (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleNavigateToStop(stop)}
                                  leftIcon={Navigation}
                                  disabled={!stop.latitude || !stop.longitude}
                                  className="w-full sm:w-auto"
                                >
                                  Navigate
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCompleteStop(index)}
                                  leftIcon={Check}
                                  className="w-full sm:w-auto"
                                >
                                  Done
                                </Button>
                              </>
                            )}
                          </div>
                        </div>

                        {/* Separator and Distance Info */}
                        {index < optimizedRoute.optimizedStops.length - 1 && stop.distanceToNext && stop.timeToNext && (
                          <>
                            <Separator />
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Route className="h-4 w-4" />
                              <span>To next stop: {formatTime(stop.timeToNext)} / {formatDistance(stop.distanceToNext)} miles</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Finish Route Button */}
              <div className="flex justify-center pt-4">
                <Button 
                  onClick={handleFinishAndStartNewRoute}
                  className="min-w-[200px]"
                >
                  Finish Route & Start New
                </Button>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="routes" className="space-y-4">
          {routesLoading ? (
            <div className="flex items-center justify-center py-8">
              <Spinner />
            </div>
          ) : routesError ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Failed to load routes</p>
              <p className="text-sm text-red-500">{routesError}</p>
            </div>
          ) : recentRoutes.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No routes found</p>
              <p className="text-sm text-muted-foreground">Start by creating your first route!</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {recentRoutes.map((route) => (
                <Card key={route.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <h3 className="font-semibold">Started: {new Date(route.startedAt).toLocaleString()}</h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {route.totalStops} stops
                          </span>
                          <span className="flex items-center gap-1">
                            <Route className="h-3 w-3" />
                            {route.totalDistance} miles
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatTime(route.totalDuration)}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(route.status)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      {/* Subscription Limit Dialog */}
      {limitDialog.limitResult && (
        <SubscriptionLimitDialog
          isOpen={limitDialog.isOpen}
          onClose={() => setLimitDialog({ isOpen: false, limitResult: null })}
          limitResult={limitDialog.limitResult}
          planTier={subscriptionLimits.planTier}
          feature={limitDialog.feature}
        />
      )}
    </div>
  );
} 