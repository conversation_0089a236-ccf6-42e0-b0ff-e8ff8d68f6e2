import { userRouter } from "@/server/api/routers/user";
import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";
import { utImageRouter } from "./routers/utImage";
import { polarRouter } from "./routers/polar";
import { authRouter } from "./routers/auth";
import { adminRouter } from "./routers/admin";
import { geocodingRouter } from "./routers/geocoding";
import { imageProcessingRouter } from "./routers/imageProcessing";
import { routesRouter } from "./routers/routes";
import { preferencesRouter } from "./routers/preferences";

export const appRouter = createTRPCRouter({
  user: userRouter,
  utImage: utImageRouter,
  polar: polarRouter,
  auth: authRouter,
  admin: adminRouter,
  geocoding: geocodingRouter,
  imageProcessing: imageProcessingRouter,
  routes: routesRouter,
  preferences: preferencesRouter,
});

export type AppRouter = typeof appRouter;
export const createCaller = createCallerFactory(appRouter);
