import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const preferencesRouter = createTRPCRouter({
  /**
   * Get user preferences
   */
  getPreferences: protectedProcedure
    .query(async ({ ctx }) => {
      const preferences = await ctx.db.userPreferences.findUnique({
        where: { userId: ctx.session.user.id },
      });

      // Return default preferences if none exist
      if (!preferences) {
        return {
          navigationApp: "GOOGLE_MAPS",
          theme: "SYSTEM",
          language: "ENGLISH",
        };
      }

      return preferences;
    }),

  /**
   * Update user preferences
   */
  updatePreferences: protectedProcedure
    .input(
      z.object({
        navigationApp: z.enum(["GOOGLE_MAPS", "APPLE_MAPS"]).optional(),
        theme: z.enum(["LIGHT", "DARK", "SYSTEM"]).optional(),
        language: z.enum(["ENGLISH", "RUSSIAN"]).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const preferences = await ctx.db.userPreferences.upsert({
          where: { userId: ctx.session.user.id },
          create: {
            userId: ctx.session.user.id,
            navigationApp: input.navigationApp || "GOOGLE_MAPS",
            theme: input.theme || "SYSTEM",
            language: input.language || "ENGLISH",
          },
          update: {
            ...(input.navigationApp && { navigationApp: input.navigationApp }),
            ...(input.theme && { theme: input.theme }),
            ...(input.language && { language: input.language }),
          },
        });

        return preferences;
      } catch (error) {
        console.error("Preferences update error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update preferences",
        });
      }
    }),

  /**
   * Get user usage stats and subscription info
   */
  getUserStats: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      const [usageStats, subscription] = await Promise.all([
        ctx.db.usageStats.findUnique({
          where: { userId },
        }),
        ctx.db.subscription.findUnique({
          where: { userId },
        }),
      ]);

      const routeCount = await ctx.db.route.count({
        where: { userId },
      });

      const activeRouteCount = await ctx.db.route.count({
        where: { 
          userId,
          status: "ACTIVE",
        },
      });

      // Calculate current month's usage
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      
      const currentMonthRoutes = await ctx.db.route.count({
        where: {
          userId,
          createdAt: {
            gte: startOfMonth,
          },
        },
      });

      // Determine plan limits
      let monthlyRouteLimit = 0;
      let maxStopsPerRoute = 0;
      
      if (subscription) {
        if (subscription.plan === "PRO") {
          monthlyRouteLimit = 50;
          maxStopsPerRoute = 20;
        } else if (subscription.plan === "UNLIMITED") {
          monthlyRouteLimit = Infinity;
          maxStopsPerRoute = Infinity;
        }
      }

      return {
        usage: {
          routesOptimizedThisMonth: usageStats?.routesOptimizedThisMonth || currentMonthRoutes,
          totalRoutes: routeCount,
          activeRoutes: activeRouteCount,
        },
        subscription: subscription ? {
          plan: subscription.plan,
          status: subscription.status,
          currentPeriodStart: subscription.currentPeriodStart,
          currentPeriodEnd: subscription.currentPeriodEnd,
          trialEndsAt: subscription.trialEndsAt,
          cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        } : null,
        limits: {
          monthlyRouteLimit,
          maxStopsPerRoute,
          isUnlimited: subscription?.plan === "UNLIMITED",
        },
      };
    }),

  /**
   * Reset monthly usage stats (for testing or admin purposes)
   */
  resetMonthlyUsage: protectedProcedure
    .mutation(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      try {
        await ctx.db.usageStats.upsert({
          where: { userId },
          create: {
            userId,
            routesOptimizedThisMonth: 0,
            lastResetDate: new Date(),
          },
          update: {
            routesOptimizedThisMonth: 0,
            lastResetDate: new Date(),
          },
        });

        return { success: true };
      } catch (error) {
        console.error("Usage reset error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to reset usage stats",
        });
      }
    }),
}); 