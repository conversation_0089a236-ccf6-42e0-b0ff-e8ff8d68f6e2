import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/server/api/trpc";
import { extractAddressesFromText, isValidAddress, normalizeAddress } from "@/lib/imageParser";
import { TRPCError } from "@trpc/server";

export const imageProcessingRouter = createTRPCRouter({
  /**
   * Extract addresses from text (for when OCR is done client-side)
   */
  extractAddressesFromText: protectedProcedure
    .input(
      z.object({
        text: z.string().min(1, "Text is required"),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const addresses = extractAddressesFromText(input.text);
        
        return {
          addresses,
          count: addresses.length,
        };
      } catch (error) {
        console.error("Address extraction error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to extract addresses from text",
        });
      }
    }),

  /**
   * Validate and normalize addresses
   */
  validateAddresses: protectedProcedure
    .input(
      z.object({
        addresses: z.array(z.string()).max(100, "Too many addresses"),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const validatedAddresses = input.addresses.map((address) => {
          const isValid = isValidAddress(address);
          const normalized = isValid ? normalizeAddress(address) : address;
          
          return {
            original: address,
            normalized,
            isValid,
          };
        });

        const validCount = validatedAddresses.filter(addr => addr.isValid).length;

        return {
          addresses: validatedAddresses,
          validCount,
          totalCount: input.addresses.length,
        };
      } catch (error) {
        console.error("Address validation error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to validate addresses",
        });
      }
    }),

  /**
   * Process multiple texts (from multiple images) and extract unique addresses
   */
  processMultipleTexts: protectedProcedure
    .input(
      z.object({
        texts: z.array(z.string()).min(1, "At least one text is required").max(15, "Too many texts"),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const allAddresses: Array<{
          address: string;
          confidence: number;
          lineNumber: number;
          sourceIndex: number;
        }> = [];

        // Extract addresses from each text
        input.texts.forEach((text, sourceIndex) => {
          const addresses = extractAddressesFromText(text);
          addresses.forEach(addr => {
            allAddresses.push({
              ...addr,
              sourceIndex,
            });
          });
        });

        // Remove duplicates across all texts
        const uniqueAddresses = allAddresses
          .filter((addr, index, arr) => 
            arr.findIndex(a => a.address.toLowerCase() === addr.address.toLowerCase()) === index
          )
          .sort((a, b) => b.confidence - a.confidence);

        return {
          addresses: uniqueAddresses,
          count: uniqueAddresses.length,
          sourceCount: input.texts.length,
        };
      } catch (error) {
        console.error("Multiple text processing error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process multiple texts",
        });
      }
    }),
}); 