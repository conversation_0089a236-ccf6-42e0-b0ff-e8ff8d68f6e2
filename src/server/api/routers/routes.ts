import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { optimizeWaypointSequence, getOptimizedRoute, formatDistance, formatDuration } from "@/lib/routing";
import { geocodeAddresses } from "@/lib/geocoding";
import { TRPCError } from "@trpc/server";

// Import RouteStatus from generated Prisma types
import type { RouteStatus } from "@/generated/prisma";

const routePointSchema = z.object({
  address: z.string(),
  latitude: z.number(),
  longitude: z.number(),
});

export const routesRouter = createTRPCRouter({
  /**
   * Create and optimize a new route
   */
  createOptimizedRoute: protectedProcedure
    .input(
      z.object({
        name: z.string().optional(),
        startingAddress: z.string().min(1, "Starting address is required"),
        destinationAddress: z.string().optional(),
        stopAddresses: z.array(z.string()).min(1, "At least one stop is required").max(50, "Too many stops"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        // Check user's subscription limits
        const userStats = await ctx.db.usageStats.findUnique({
          where: { userId },
          include: { user: { include: { subscription: true } } },
        });

        if (userStats?.user.subscription) {
          const subscription = userStats.user.subscription;
          if (subscription.plan === "PRO" && userStats.routesOptimizedThisMonth >= 50) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "Monthly route limit reached for Pro plan",
            });
          }
        }

        // Geocode all addresses
        const allAddresses = [input.startingAddress, ...input.stopAddresses];
        if (input.destinationAddress) {
          allAddresses.push(input.destinationAddress);
        }

        const geocodedResults = await geocodeAddresses(allAddresses);
        
        // Check for failed geocoding
        const failedAddresses = geocodedResults
          .map((result, index) => ({ result, address: allAddresses[index] }))
          .filter(item => !item.result)
          .map(item => item.address);

        if (failedAddresses.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Failed to geocode addresses: ${failedAddresses.join(", ")}`,
          });
        }

        const [startGeocode, ...stopGeocodes] = geocodedResults.filter(Boolean);
        const destinationGeocode = input.destinationAddress ? stopGeocodes.pop() : undefined;
        const validStopGeocodes = stopGeocodes.filter(Boolean);

        if (!startGeocode) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Failed to geocode starting address",
          });
        }

        // Optimize waypoint sequence
        const startPoint = {
          latitude: startGeocode.latitude,
          longitude: startGeocode.longitude,
          address: startGeocode.address,
        };

        const waypoints = validStopGeocodes.map(geocode => ({
          latitude: geocode!.latitude,
          longitude: geocode!.longitude,
          address: geocode!.address,
        }));

        const destinationPoint = destinationGeocode ? {
          latitude: destinationGeocode.latitude,
          longitude: destinationGeocode.longitude,
          address: destinationGeocode.address,
        } : undefined;

        const optimizedOrder = await optimizeWaypointSequence(startPoint, waypoints, destinationPoint);
        const optimizedRouteInfo = await getOptimizedRoute(startPoint, waypoints, optimizedOrder, destinationPoint);

        // Create route in database
        const route = await ctx.db.route.create({
          data: {
            userId,
            name: input.name || `Route ${new Date().toLocaleDateString()}`,
            startingAddress: startGeocode.address,
            startingLatitude: startGeocode.latitude,
            startingLongitude: startGeocode.longitude,
            destinationAddress: destinationPoint?.address,
            destinationLatitude: destinationPoint?.latitude,
            destinationLongitude: destinationPoint?.longitude,
            optimizedOrder,
            totalDistance: optimizedRouteInfo.totalDistance,
            estimatedDuration: optimizedRouteInfo.totalDuration,
            status: "ACTIVE" as RouteStatus,
            stops: {
              create: waypoints.map((waypoint, index) => ({
                address: waypoint.address,
                latitude: waypoint.latitude,
                longitude: waypoint.longitude,
                orderIndex: index,
                completed: false,
              })),
            },
          },
          include: {
            stops: true,
          },
        });

        // Update usage stats
        await ctx.db.usageStats.upsert({
          where: { userId },
          create: {
            userId,
            routesOptimizedThisMonth: 1,
          },
          update: {
            routesOptimizedThisMonth: { increment: 1 },
          },
        });

        return {
          route,
          optimization: {
            originalOrder: waypoints.map((_, index) => index),
            optimizedOrder,
            totalDistance: optimizedRouteInfo.totalDistance,
            totalDuration: optimizedRouteInfo.totalDuration,
            formattedDistance: formatDistance(optimizedRouteInfo.totalDistance),
            formattedDuration: formatDuration(optimizedRouteInfo.totalDuration),
            legs: optimizedRouteInfo.legs,
          },
        };
      } catch (error) {
        console.error("Route optimization error:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create optimized route",
        });
      }
    }),

  /**
   * Get user's routes
   */
  getUserRoutes: protectedProcedure
    .input(
      z.object({
        status: z.enum(["ACTIVE", "COMPLETED", "ARCHIVED"]).optional(),
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      const routes = await ctx.db.route.findMany({
        where: {
          userId: ctx.session.user.id,
          status: input.status,
        },
        include: {
          stops: {
            orderBy: { orderIndex: "asc" },
          },
        },
        orderBy: { createdAt: "desc" },
        take: input.limit,
        skip: input.offset,
      });

      const total = await ctx.db.route.count({
        where: {
          userId: ctx.session.user.id,
          status: input.status,
        },
      });

      return {
        routes: routes.map(route => ({
          id: route.id,
          userId: route.userId,
          name: route.name,
          startingAddress: route.startingAddress,
          startingLatitude: route.startingLatitude,
          startingLongitude: route.startingLongitude,
          destinationAddress: route.destinationAddress,
          destinationLatitude: route.destinationLatitude,
          destinationLongitude: route.destinationLongitude,
          optimizedOrder: route.optimizedOrder,
          totalDistance: route.totalDistance,
          estimatedDuration: route.estimatedDuration,
          status: route.status,
          stops: route.stops,
          createdAt: route.createdAt,
          updatedAt: route.updatedAt,
          formattedDistance: route.totalDistance ? formatDistance(route.totalDistance) : null,
          formattedDuration: route.estimatedDuration ? formatDuration(route.estimatedDuration) : null,
        })),
        total,
        hasMore: input.offset + input.limit < total,
      };
    }),

  /**
   * Get a specific route by ID
   */
  getRoute: protectedProcedure
    .input(z.object({ routeId: z.string() }))
    .query(async ({ ctx, input }) => {
      const route = await ctx.db.route.findFirst({
        where: {
          id: input.routeId,
          userId: ctx.session.user.id,
        },
        include: {
          stops: {
            orderBy: { orderIndex: "asc" },
          },
        },
      });

      if (!route) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Route not found",
        });
      }

      return {
        id: route.id,
        userId: route.userId,
        name: route.name,
        startingAddress: route.startingAddress,
        startingLatitude: route.startingLatitude,
        startingLongitude: route.startingLongitude,
        destinationAddress: route.destinationAddress,
        destinationLatitude: route.destinationLatitude,
        destinationLongitude: route.destinationLongitude,
        optimizedOrder: route.optimizedOrder,
        totalDistance: route.totalDistance,
        estimatedDuration: route.estimatedDuration,
        status: route.status,
        stops: route.stops,
        createdAt: route.createdAt,
        updatedAt: route.updatedAt,
        formattedDistance: route.totalDistance ? formatDistance(route.totalDistance) : null,
        formattedDuration: route.estimatedDuration ? formatDuration(route.estimatedDuration) : null,
      };
    }),

  /**
   * Mark a stop as completed
   */
  completeStop: protectedProcedure
    .input(
      z.object({
        routeId: z.string(),
        stopId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const route = await ctx.db.route.findFirst({
        where: {
          id: input.routeId,
          userId: ctx.session.user.id,
        },
        include: { stops: true },
      });

      if (!route) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Route not found",
        });
      }

      const stop = route.stops.find(s => s.id === input.stopId);
      if (!stop) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Stop not found",
        });
      }

      const updatedStop = await ctx.db.routeStop.update({
        where: { id: input.stopId },
        data: { completed: true },
      });

      // Check if all stops are completed
      const allStops = await ctx.db.routeStop.findMany({
        where: { routeId: input.routeId },
      });

      const allCompleted = allStops.every(stop => stop.completed);

      // Update route status if all stops are completed
      if (allCompleted) {
        await ctx.db.route.update({
          where: { id: input.routeId },
          data: { status: "COMPLETED" as RouteStatus },
        });
      }

      return {
        stop: updatedStop,
        allCompleted,
      };
    }),

  /**
   * Mark a stop as not completed
   */
  uncompleteStop: protectedProcedure
    .input(
      z.object({
        routeId: z.string(),
        stopId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const route = await ctx.db.route.findFirst({
        where: {
          id: input.routeId,
          userId: ctx.session.user.id,
        },
        include: { stops: true },
      });

      if (!route) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Route not found",
        });
      }

      const stop = route.stops.find(s => s.id === input.stopId);
      if (!stop) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Stop not found",
        });
      }

      const updatedStop = await ctx.db.routeStop.update({
        where: { id: input.stopId },
        data: { completed: false },
      });

      // Update route status to active if it was completed
      if (route.status === "COMPLETED") {
        await ctx.db.route.update({
          where: { id: input.routeId },
          data: { status: "ACTIVE" as RouteStatus },
        });
      }

      return { stop: updatedStop };
    }),

  /**
   * Finish a route (mark as completed)
   */
  finishRoute: protectedProcedure
    .input(z.object({ routeId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const route = await ctx.db.route.findFirst({
        where: {
          id: input.routeId,
          userId: ctx.session.user.id,
        },
      });

      if (!route) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Route not found",
        });
      }

      const updatedRoute = await ctx.db.route.update({
        where: { id: input.routeId },
        data: { status: "COMPLETED" as RouteStatus },
      });

      return updatedRoute;
    }),

  /**
   * Delete a route
   */
  deleteRoute: protectedProcedure
    .input(z.object({ routeId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const route = await ctx.db.route.findFirst({
        where: {
          id: input.routeId,
          userId: ctx.session.user.id,
        },
      });

      if (!route) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Route not found",
        });
      }

      await ctx.db.route.delete({
        where: { id: input.routeId },
      });

      return { success: true };
    }),
}); 