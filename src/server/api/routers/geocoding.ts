import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/server/api/trpc";
import { geocodeAddress, geocodeAddresses, reverseGeocode } from "@/lib/geocoding";
import { TRPCError } from "@trpc/server";

export const geocodingRouter = createTRPCRouter({
  /**
   * Geocode a single address
   */
  geocodeAddress: protectedProcedure
    .input(
      z.object({
        address: z.string().min(1, "Address is required"),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const result = await geocodeAddress(input.address);
        
        if (!result) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Address not found",
          });
        }

        return result;
      } catch (error) {
        console.error("Geocoding error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to geocode address",
        });
      }
    }),

  /**
   * Geocode multiple addresses in batch
   */
  geocodeAddresses: protectedProcedure
    .input(
      z.object({
        addresses: z.array(z.string()).min(1, "At least one address is required").max(50, "Too many addresses"),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const results = await geocodeAddresses(input.addresses);
        
        // Filter out null results and return successful geocoding results
        const successfulResults = results.map((result, index) => ({
          originalAddress: input.addresses[index],
          result,
          success: result !== null,
        }));

        return {
          results: successfulResults,
          successCount: successfulResults.filter(r => r.success).length,
          totalCount: input.addresses.length,
        };
      } catch (error) {
        console.error("Batch geocoding error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to geocode addresses",
        });
      }
    }),

  /**
   * Reverse geocode coordinates to get address
   */
  reverseGeocode: protectedProcedure
    .input(
      z.object({
        latitude: z.number().min(-90).max(90),
        longitude: z.number().min(-180).max(180),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const address = await reverseGeocode(input.latitude, input.longitude);
        
        if (!address) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No address found for these coordinates",
          });
        }

        return { address };
      } catch (error) {
        console.error("Reverse geocoding error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to reverse geocode coordinates",
        });
      }
    }),
}); 