# Geocoding Cache System

## Overview

The geocoding cache system is designed to reduce API usage and improve performance by caching address geocoding results locally in the browser. This system automatically stores successful geocoding results and reuses them for subsequent requests to the same addresses.

## Features

### Smart Caching
- **Automatic Storage**: Successfully geocoded addresses are automatically cached
- **Intelligent Key Normalization**: Addresses are normalized to improve cache hit rates
- **Expiration Management**: Cached entries expire after 7 days
- **Size Limits**: Maximum of 1,000 cached entries to prevent unlimited storage growth

### Performance Benefits
- **Reduced API Calls**: Cached addresses don't require new API requests
- **Faster Lookups**: Instant results for previously geocoded addresses
- **Offline Availability**: Cached addresses work without internet connection
- **Cost Savings**: Reduces Mapbox API usage and associated costs

### Cache Types
1. **Forward Geocoding**: Address → Coordinates
2. **Reverse Geocoding**: Coordinates → Address (with coordinate rounding for efficiency)
3. **Current Location**: Browser geolocation results

## Implementation Details

### Cache Storage
- **Method**: Browser localStorage
- **Key**: `optiroute-geocoding-cache`
- **Format**: JSON with expiration timestamps
- **Persistence**: Survives browser sessions and restarts

### Address Normalization
The system normalizes addresses to improve cache hit rates:
- Converts to lowercase
- Trims whitespace
- Removes common punctuation (., #)
- Standardizes street abbreviations (Street → St, Avenue → Ave, etc.)

### Cache Management
- **Automatic Cleanup**: Runs every hour to remove expired entries
- **Size Enforcement**: Removes oldest entries when limit is exceeded
- **Error Handling**: Gracefully handles storage errors and corruption

### API Integration
The cache is integrated into:
- `geocodeAddresses()` in route-optimization.ts
- `geocodeAddress()` and `geocodeAddresses()` in geocoding.ts
- `reverseGeocode()` for current location features

## Usage

### Automatic Operation
The cache works automatically without any developer intervention:

```typescript
// This will check cache first, then API if needed
const stops = await geocodeAddresses(['123 Main St', '456 Oak Ave']);

// Cache hits will be instant, misses will call Mapbox API and cache results
```

### Cache Statistics
Use the `GeocodingCacheManager` component to view cache performance:

```typescript
import { GeocodingCacheManager } from '@/components/GeocodingCacheManager';

// Add to settings page or admin panel
<GeocodingCacheManager />
```

### Manual Cache Control
```typescript
import { GeocodingCache } from '@/lib/geocoding-cache';

const cache = GeocodingCache.getInstance();

// Check if address is cached
const isCached = cache.has('123 Main St');

// Get cache statistics
const stats = cache.getStats();

// Clear all cached data
cache.clear();

// Export cache for debugging
const entries = cache.exportCache();
```

## Performance Impact

### Before Caching
- Every address lookup requires API call
- Network latency for each request
- API rate limits may slow down bulk operations
- Costs accumulate with repeated lookups

### After Caching
- Common addresses (home, office, frequent destinations) load instantly
- Bulk operations are faster for mixed new/cached addresses
- Significant reduction in API calls for repeat users
- Lower costs due to reduced API usage

## Cache Effectiveness

### High Cache Hit Scenarios
- Users with regular delivery routes
- Repeated use of company/warehouse addresses
- Common destinations within a service area
- Users who frequently create similar routes

### Cache Miss Scenarios
- First-time address entry
- Unique, one-time destinations
- Addresses with different formatting
- Expired cache entries (after 7 days)

## Configuration

### Customizable Settings
Current settings can be modified in `src/lib/geocoding-cache.ts`:

```typescript
const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days
const MAX_CACHE_ENTRIES = 1000; // Maximum entries
```

### Storage Requirements
- Average entry size: ~200-300 bytes
- 1,000 entries: ~200-300 KB total
- Well within localStorage limits (5-10 MB typically)

## Error Handling

### Graceful Degradation
- Cache failures don't break geocoding functionality
- Falls back to direct API calls if cache is unavailable
- Automatic cleanup of corrupted cache data
- Non-blocking cache operations

### Error Scenarios
1. **localStorage Full**: Automatic cleanup of old entries
2. **Cache Corruption**: Clears cache and starts fresh
3. **API Failures**: Doesn't cache failed results
4. **Browser Compatibility**: Checks for localStorage support

## Security Considerations

### Data Privacy
- Only addresses and coordinates are cached (no personal data)
- Local storage only (not transmitted to servers)
- Automatic expiration ensures data freshness
- User can manually clear cache anytime

### Data Integrity
- Timestamp-based expiration
- Version-aware cache structure
- Validation of cached data before use
- Protection against cache poisoning

## Monitoring and Analytics

### Cache Performance Metrics
- Cache hit rate
- Storage usage
- Entry age distribution
- API call reduction percentage

### Development Tools
- Browser console logging for cache operations
- Cache statistics component for admin interface
- Export functionality for debugging
- Manual cache management controls

## Future Enhancements

### Potential Improvements
1. **Smart Prefetching**: Preload common addresses
2. **Compression**: Reduce storage footprint
3. **Sync**: Cloud backup for premium users
4. **Analytics**: Detailed cache performance metrics
5. **Regional Optimization**: Location-aware cache strategies

### Migration Path
The cache system is designed to be backward compatible and can be safely updated without breaking existing functionality. 