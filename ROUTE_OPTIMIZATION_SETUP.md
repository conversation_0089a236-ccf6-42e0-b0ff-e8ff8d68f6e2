# Route Optimization System Setup

This document explains how to set up and use the route optimization system that includes real routing, waypoint sequencing, and interactive maps.

## Required Environment Variables

Add these environment variables to your `.env.local` file:

```env
# Mapbox API (Required for geocoding and route optimization)
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoieW91cmFjY291bnQiLCJhIjoiY2xxcXJvaWJrMDJ3eDJqcGd2ODZqYzNyOCJ9.example

# HERE API (Required for optimization >12 stops)
NEXT_PUBLIC_HERE_API_KEY=your_here_api_key_here

# OpenRouter API (Required for AI image address extraction)
NEXT_PUBLIC_OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key
```

## API Key Setup

### 1. Mapbox Access Token
1. Go to [Mapbox Account](https://account.mapbox.com/access-tokens/)
2. Create a new **public access token** (for client-side use)
3. The default public token has all necessary scopes:
   - Geocoding API access
   - Directions API access  
   - Optimization API access
4. Copy the access token to `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN`

**Note:** We use Mapbox APIs for geocoding and routing, but we display maps using **MapLibre GL JS with OpenStreetMap tiles** - so you don't need Mapbox's map styling services.

### 2. HERE API Key
1. Go to [HERE Developer Portal](https://developer.here.com/projects)
2. Create a new project
3. Generate an API key with these services:
   - `Routing API v8` (for basic routing)
   - `Matrix Routing API v8` (for distance/time calculations)
   - `Waypoints Sequence API` (for route optimization >12 stops)
   - `Geocoding & Search API` (optional, using Mapbox for geocoding)
4. Copy the API key to `NEXT_PUBLIC_HERE_API_KEY`

### 3. OpenRouter API Key
1. Go to [OpenRouter](https://openrouter.ai/keys)
2. Create an account and generate an API key
3. Copy the API key to `NEXT_PUBLIC_OPENROUTER_API_KEY`

## How It Works

### Route Optimization Flow

1. **Address Input**: User enters starting address, stops, and destination
2. **Current Location**: Optional GPS-based current location detection
3. **Image Upload**: AI-powered address extraction from uploaded images
4. **Geocoding**: All addresses are converted to coordinates using Mapbox Geocoding API
5. **Original Route Calculation**: Calculate baseline route distance/time using Mapbox Directions API
6. **Route Optimization**: 
   - ≤12 stops: Uses Mapbox Optimization API
   - >12 stops: Uses HERE Waypoint Sequencing API
7. **Optimized Route Calculation**: Calculate new route distance/time
8. **Savings Calculation**: Compare original vs optimized routes
9. **Interactive Map**: Display route with markers and polylines using MapLibre GL JS
10. **Navigation**: Integration with Google Maps/Apple Maps

### Features

#### Create Route Tab
- Drag-and-drop reordering of stops
- Current location detection with crosshair button
- AI image address extraction
- Form validation and error handling

#### Optimized Route Tab
- Real savings calculations (distance, time, percentage)
- Interactive **MapLibre GL JS** map with **OpenStreetMap tiles**:
  - Numbered markers (green=start, red=end, blue=active, gray=completed)
  - Polylines showing actual route path from routing APIs
  - Click markers for address popups
  - Modern, clean styling
- Stop management:
  - Navigate buttons (opens preferred navigation app)
  - Done/Undo completion tracking
  - Mobile-responsive design

#### My Routes Tab
- Historical route data
- Performance metrics

### Technical Implementation

#### Route Optimization (`src/lib/route-optimization.ts`)
- Real geocoding with Mapbox Geocoding API
- Original route calculation for baseline metrics using Mapbox Directions API
- Smart API selection (Mapbox vs HERE based on stop count)
- Polyline decoding for accurate route display
- Distance conversion (meters/km to miles)
- Error handling and fallbacks

#### Interactive Map (`src/components/RouteMap.tsx`)
- **MapLibre GL JS** with **OpenStreetMap tiles** (open source alternative to Mapbox maps)
- Proper marker styling with completion states
- Polyline decoding using `@mapbox/polyline`
- Click interactions and popups
- Responsive design

#### Main Interface (`src/app/(app)/(scrollable)/routes/page.tsx`)
- Three-tab interface (Create, Optimized, My Routes)
- Drag-and-drop with visual feedback
- Current location integration
- Progress tracking during optimization
- Mobile-responsive layouts

### API Integration

#### Mapbox APIs Used
- **Geocoding API**: Convert addresses to coordinates
- **Directions API**: Calculate route distance/time/polylines
- **Optimization API**: Waypoint sequencing for ≤12 stops

**Important:** We only use Mapbox for **data services** (geocoding, routing, optimization). The **map display** uses MapLibre GL JS with OpenStreetMap tiles for a completely open-source mapping solution.

#### HERE APIs Used
- **Routing API v8**: Route calculation and optimization for >12 stops
- **Matrix Routing API v8**: Distance/time calculations between multiple points
- **Waypoints Sequence API**: Optimal ordering of waypoints for large routes

#### OpenRouter API Used
- **GPT-4 Vision**: Extract addresses from uploaded images

#### Map Display
- **MapLibre GL JS**: Open-source map rendering library
- **OpenStreetMap**: Free, open-source map tiles
- No additional map API keys needed for display

### Error Handling

- Graceful fallbacks for missing API keys
- Network error handling with user feedback
- Invalid address handling
- Map rendering fallbacks

### Mobile Optimization

- Responsive button layouts (vertical stacking on mobile)
- Touch-friendly drag handles
- Optimized map interactions
- Proper viewport scaling

## Testing

To test the system:

1. Add environment variables (you only need Mapbox token for basic functionality)
2. Create a route with 3-5 stops
3. Check that optimization actually reorders stops
4. Verify map shows markers and polylines
5. Test navigation integration
6. Check savings calculations are realistic

## Troubleshooting

### Common Issues

1. **No optimization happening**: Check Mapbox API key and network requests
2. **Map not loading**: MapLibre should work without any API keys - check console for errors
3. **No polylines**: Check polyline decoding and API responses from Mapbox Directions
4. **Savings showing 0%**: Ensure original route calculation is working
5. **Geocoding fails**: Verify Mapbox access token is valid

### Debug Tools

- Browser Developer Tools Network tab
- Console logs for API responses
- Environment variable validation in app startup

### API Usage Notes

- **Mapbox**: Free tier includes 100,000 geocoding requests and 50,000 direction requests per month
- **HERE**: Offers free tier with 250,000 API calls per month
- **OpenRouter**: Pay-per-use pricing for AI vision model
- **MapLibre + OpenStreetMap**: Completely free with no API limits 