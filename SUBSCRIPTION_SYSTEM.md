# OptiRoutePro Subscription System

## Overview
Comprehensive subscription management system with plan-based limitations, usage tracking, and upgrade flows integrated with Polar.sh billing. Features 7-day free trials for all plans.

## 🏗️ Architecture

### Core Components

#### 1. Subscription Limits Service (`src/lib/subscription-limits.ts`)
- **PlanTier Enum**: TRIAL, PRO, UNLIMITED
- **PlanLimits Interface**: Defines all plan restrictions and features
- **SubscriptionLimitsService Class**: Static methods for limit checking and validation

#### 2. Subscription Limits Hook (`src/hooks/useSubscriptionLimits.ts`)
- React hook providing subscription data and limit checking functions
- Integrates with existing Polar billing system
- Provides real-time plan tier detection and usage statistics

#### 3. UI Components
- **SubscriptionLimitDialog**: Modal for limit warnings and upgrade prompts
- **SubscriptionTestPanel**: Comprehensive testing interface for all limits
- **Plan status indicators**: Integrated into main routes page

## 📊 Plan Tiers & Limits

### TRIAL Plan (7-day free trial)
```typescript
{
  monthlyRouteLimit: 10,
  maxStopsPerRoute: 25,
  maxImagesPerUpload: 50,
  canUseImageUpload: true,
  canUseBulkOptimization: true,
  canExportRoutes: true,
  canUseAdvancedAnalytics: true,
  maxGeocodingRequestsPerDay: 200,
  maxOptimizationRequestsPerHour: 20,
  maxSavedRoutes: 25,
  routeHistoryDays: 30,
  trialDays: 7,
}
```

### PRO Plan ($9/month or $90/year)
```typescript
{
  monthlyRouteLimit: 50,
  maxStopsPerRoute: 55,
  maxImagesPerUpload: 500,
  canUseImageUpload: true,
  canUseBulkOptimization: true,
  canExportRoutes: true,
  canUseAdvancedAnalytics: true,
  maxGeocodingRequestsPerDay: 2000,
  maxOptimizationRequestsPerHour: 100,
  maxSavedRoutes: 200,
  routeHistoryDays: 365,
}
```

### UNLIMITED Plan ($29/month or $290/year)
```typescript
{
  monthlyRouteLimit: Infinity,
  maxStopsPerRoute: 100,
  maxImagesPerUpload: Infinity,
  canUseImageUpload: true,
  canUseBulkOptimization: true,
  canExportRoutes: true,
  canUseAdvancedAnalytics: true,
  maxGeocodingRequestsPerDay: Infinity,
  maxOptimizationRequestsPerHour: Infinity,
  maxSavedRoutes: Infinity,
  routeHistoryDays: Infinity,
}
```

## 💰 Pricing Structure

| Feature | PRO | UNLIMITED |
|---------|-----|-----------|
| **Monthly Routes** | 50 | ∞ |
| **Max Stops/Route** | 55 | 100 |
| **Image Uploads** | 500 | ∞ |
| **Bulk Optimization** | ✅ | ✅ |
| **Advanced Analytics** | ✅ | ✅ |
| **Export Routes** | ✅ | ✅ |
| **7-day Free Trial** | ✅ | ✅ |
| **Monthly Price** | $9 | $29 |

## 🔧 Implementation Details

### Plan Tier Detection
```typescript
const planTier = useMemo((): PlanTier => {
  if (!billingState || !isPro) {
    return PlanTier.TRIAL;
  }
  
  // Check if user has unlimited plan ($29+ monthly)
  const hasUnlimitedSubscription = billingState.activeSubscriptions?.some(
    sub => sub.amount >= 2900 // $29+ indicates unlimited plan
  );
  
  if (hasUnlimitedSubscription) {
    return PlanTier.UNLIMITED;
  }
  
  return isPro ? PlanTier.PRO : PlanTier.TRIAL;
}, [billingState, isPro]);
```

### Limit Checking
```typescript
// Route optimization limit check
const limitCheck = subscriptionLimits.checkRouteOptimization(totalStops);
if (!limitCheck.allowed) {
  // Show upgrade dialog
  setLimitDialog({
    isOpen: true,
    limitResult: limitCheck,
    feature: "Route Optimization",
  });
  return;
}
```

### Usage Statistics (Mock Implementation)
Currently using mock data for demonstration. In production, this would integrate with:
- Database usage tracking
- Real-time API call counting
- Monthly usage reset logic

## 🎯 Integration Points

### 1. Routes Page (`src/app/(app)/(scrollable)/routes/page.tsx`)
- **Pre-optimization checks**: Validates route limits before API calls
- **Image upload limits**: Restricts number of images based on plan
- **Visual indicators**: Shows current usage and plan status
- **Upgrade prompts**: Contextual upgrade suggestions

### 2. Pricing Page (`src/app/(landing)/pricing/page.tsx`)
- **Plan comparison**: Side-by-side comparison of Pro vs Unlimited
- **7-day trial emphasis**: All plans include free trial
- **Route optimization focus**: Features tailored to delivery businesses

### 3. Testing Interface (`/subscription-test`)
- **Comprehensive testing**: All limit types and scenarios
- **Feature access testing**: Bulk optimization, export, analytics
- **Visual feedback**: Real-time limit checking and upgrade prompts

## 🚀 Usage Examples

### Basic Limit Checking
```typescript
const subscriptionLimits = useSubscriptionLimits();

// Check if user can optimize a route with 60 stops
const canOptimize = subscriptionLimits.checkRouteOptimization(60);
if (!canOptimize.allowed) {
  console.log(canOptimize.reason); // "Too many stops (60). Maximum allowed: 55"
}

// Check if user can upload 600 images
const canUpload = subscriptionLimits.checkImageUpload(600);
if (!canUpload.allowed) {
  console.log(canUpload.reason); // "Too many images (600). Maximum allowed: 500"
}
```

### Feature Access Control
```typescript
// All plans have access to all features
const canExport = subscriptionLimits.checkFeatureAccess('exportRoutes'); // Always true
const canAnalyze = subscriptionLimits.checkFeatureAccess('advancedAnalytics'); // Always true
const canBulkOptimize = subscriptionLimits.checkFeatureAccess('bulkOptimization'); // Always true
```

### Usage Monitoring
```typescript
// Get current usage statistics
const usage = subscriptionLimits.usage;
const percentage = subscriptionLimits.getUsagePercentage(
  usage.routesOptimizedThisMonth,
  subscriptionLimits.planLimits.monthlyRouteLimit
);

// Check if user is near their limit
const isNearLimit = subscriptionLimits.isNearLimit(
  usage.routesOptimizedThisMonth,
  subscriptionLimits.planLimits.monthlyRouteLimit,
  0.8 // 80% threshold
);
```

## 🧪 Testing

### Manual Testing
1. **Visit `/subscription-test`** for comprehensive testing interface
2. **Test different scenarios**:
   - Route optimization with various stop counts (try 30, 60, 120 stops)
   - Image uploads with different quantities (try 100, 600, 1000 images)
   - Feature access for different plan tiers
3. **Verify upgrade flows** work correctly

### Automated Testing
```typescript
// Example test cases
describe('Subscription Limits', () => {
  it('should allow trial users to optimize routes with ≤25 stops', () => {
    const result = SubscriptionLimitsService.checkRouteOptimizationLimit(
      PlanTier.TRIAL,
      mockUsage,
      25
    );
    expect(result.allowed).toBe(true);
  });
  
  it('should block pro users from routes with >55 stops', () => {
    const result = SubscriptionLimitsService.checkRouteOptimizationLimit(
      PlanTier.PRO,
      mockUsage,
      60
    );
    expect(result.allowed).toBe(false);
    expect(result.upgradeRequired).toBe(true);
  });
  
  it('should allow unlimited users unlimited image uploads', () => {
    const result = SubscriptionLimitsService.checkImageUploadLimit(
      PlanTier.UNLIMITED,
      10000
    );
    expect(result.allowed).toBe(true);
  });
});
```

## 🔄 Future Enhancements

### 1. Real Usage Tracking
- Database integration for actual usage statistics
- Hourly/daily API call tracking
- Automatic monthly usage resets

### 2. Advanced Features
- Usage analytics dashboard
- Predictive limit warnings
- Custom plan configurations

### 3. Enhanced UX
- Progressive disclosure of premium features
- In-app upgrade flows
- Usage trend visualization

## 🔗 Related Files

### Core Implementation
- `src/lib/subscription-limits.ts` - Main service logic
- `src/hooks/useSubscriptionLimits.ts` - React hook
- `src/components/SubscriptionLimitDialog.tsx` - Upgrade dialog
- `src/components/SubscriptionTestPanel.tsx` - Testing interface

### Integration Points
- `src/app/(app)/(scrollable)/routes/page.tsx` - Main routes page
- `src/app/(app)/(scrollable)/subscription-test/page.tsx` - Testing page
- `src/app/(landing)/pricing/page.tsx` - Pricing page
- `src/components/pricing/PricingPlans.tsx` - Pricing component
- `src/config/payment-plans.ts` - Plan configuration
- `src/hooks/useUserBillingStatus.ts` - Existing Polar integration

### Existing Polar Integration
- `src/components/settings/SettingsTabBilling.tsx` - Billing settings
- `src/server/api/routers/polar.ts` - Polar API integration
- `POLAR_SETUP.md` - Polar configuration guide

## 📝 Notes

- **7-day Free Trial**: All plans include a 7-day free trial with full feature access
- **No Free Tier**: Users start with trial and must subscribe to continue
- **Mock Data**: Currently using mock usage statistics for demonstration
- **Polar Integration**: Leverages existing Polar.sh billing system
- **Extensible Design**: Easy to add new plan tiers and limits
- **Type Safety**: Full TypeScript support with proper type definitions
- **Testing Ready**: Comprehensive testing interface and examples 