# Google OAuth Setup Guide

## 1. Create Google OAuth Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API (or Google Identity API)
4. Go to "Credentials" in the left sidebar
5. Click "Create Credentials" > "OAuth 2.0 Client IDs"
6. Configure the consent screen if not already done
7. For application type, select "Web application"
8. Add authorized JavaScript origins:
   - `http://localhost:3000` (for development)
   - `https://yourdomain.com` (for production)
9. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (for development)
   - `https://yourdomain.com/api/auth/callback/google` (for production)

## 2. Environment Variables

Add these environment variables to your `.env` file:

```bash
# Enable Google OAuth
NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION=true

# Google OAuth credentials
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

## 3. Testing

1. Start your development server: `npm run dev`
2. Navigate to `/signin` or `/signup`
3. You should now see "Continue with Google" buttons
4. Click the button to test the OAuth flow

## 4. Features

- ✅ Google OAuth integration with Better Auth
- ✅ Account linking (users can link multiple providers)
- ✅ Automatic user creation with Google profile data
- ✅ Secure redirect handling
- ✅ Error handling and user feedback

## 5. Troubleshooting

**Error: "redirect_uri_mismatch"**
- Check that your redirect URI in Google Console matches exactly: `http://localhost:3000/api/auth/callback/google`

**Error: "Access denied"**
- Check that the Google OAuth consent screen is properly configured
- Ensure your app is not in testing mode with restricted users

**Button not showing**
- Verify `NEXT_PUBLIC_ENABLE_GOOGLE_INTEGRATION=true` in your environment
- Check browser console for any JavaScript errors 