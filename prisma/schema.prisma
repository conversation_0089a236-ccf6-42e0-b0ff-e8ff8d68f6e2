generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // directUrl = env("DIRECT_URL") //uncomment for supabase
}

model User {
  id            String    @id
  createdAt     DateTime
  updatedAt     DateTime? @updatedAt
  name          String
  email         String    @unique
  emailVerified Boolean
  image         String?
  bio           String?

  // images → direct URL uploads
  avatarImageUrl String?
  coverImageUrl  String?

  // images → UploadThing keys
  avatarImageUTKey String?
  coverImageUTKey  String?

  // images → DB relations
  avatarImageId  String?   @unique
  avatarImage    UTImage?  @relation("AvatarImage", fields: [avatarImageId], references: [id], onDelete: SetNull)
  coverImageId   String?   @unique
  coverImage     UTImage?  @relation("CoverImage", fields: [coverImageId], references: [id], onDelete: SetNull)
  uploadedImages UTImage[] @relation("UploadedImages") // Relation for all uploaded images by user

  timezone String? @default("Etc/GMT")

  preferences Json? @default("{}")

  sessions Session[]
  accounts Account[]

  // better-auth-admin
  role       String    @default("user")
  banReason  String?
  banExpires DateTime?
  banned     Boolean   @default(false)

  // better-auth-username
  username        String?
  displayUsername String?

  onboarded Boolean @default(false)

  // Route optimization features
  subscription     Subscription?
  userPreferences  UserPreferences?
  routes           Route[]
  usageStats       UsageStats?

  @@unique([username])
  @@map("users")
}

model Subscription {
  id                String             @id @default(cuid())
  userId            String             @unique
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan              SubscriptionPlan
  status            SubscriptionStatus
  polarSubscriptionId String?          @unique
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  trialEndsAt        DateTime?
  cancelAtPeriodEnd  Boolean           @default(false)
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt

  @@map("subscriptions")
}

model UserPreferences {
  id                String    @id @default(cuid())
  userId            String    @unique
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  navigationApp     NavigationApp? @default(GOOGLE_MAPS)
  theme             Theme     @default(SYSTEM)
  language          Language  @default(ENGLISH)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@map("user_preferences")
}

model UsageStats {
  id                  String   @id @default(cuid())
  userId              String   @unique
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  routesOptimizedThisMonth Int   @default(0)
  lastResetDate       DateTime @default(now())
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@map("usage_stats")
}

model Route {
  id                String      @id @default(cuid())
  userId            String
  user              User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  name              String?
  startingAddress   String
  startingLatitude  Float
  startingLongitude Float
  destinationAddress   String?
  destinationLatitude  Float?
  destinationLongitude Float?
  optimizedOrder    Int[]       // Array of stop IDs in optimized order
  totalDistance     Float?      // in kilometers
  estimatedDuration Int?        // in minutes
  status            RouteStatus @default(ACTIVE)
  stops             RouteStop[]
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@index([userId])
  @@map("routes")
}

model RouteStop {
  id          String   @id @default(cuid())
  routeId     String
  route       Route    @relation(fields: [routeId], references: [id], onDelete: Cascade)
  address     String
  latitude    Float
  longitude   Float
  orderIndex  Int      // Original order before optimization
  completed   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([routeId])
  @@map("route_stops")
}

model Session {
  id             String    @id
  expiresAt      DateTime
  token          String    @unique
  createdAt      DateTime
  updatedAt      DateTime? @updatedAt
  ipAddress      String?
  userAgent      String?
  userId         String
  user           User      @relation(fields: [userId], references: [id])
  impersonatedBy String?

  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id])
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime? @updatedAt

  @@map("accounts")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime? @updatedAt

  @@map("verifications")
}

model UTImage {
  id        String   @id @default(cuid())
  key       String   @unique // The key returned by UploadThing
  userId    String
  user      User     @relation("UploadedImages", fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  usedAsAvatarByUser User? @relation("AvatarImage")
  usedAsCoverByUser  User? @relation("CoverImage")

  @@index([userId])
  @@map("ut_images")
}

// Enums
enum SubscriptionPlan {
  PRO
  UNLIMITED
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  TRIALING
}

enum NavigationApp {
  GOOGLE_MAPS
  APPLE_MAPS
}

enum Theme {
  LIGHT
  DARK
  SYSTEM
}

enum Language {
  ENGLISH
  RUSSIAN
}

enum RouteStatus {
  ACTIVE
  COMPLETED
  ARCHIVED
}
