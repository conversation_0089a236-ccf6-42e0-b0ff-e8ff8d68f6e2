# Polar Setup Guide

## ✅ Product IDs Updated!

I've updated the payment plans configuration with your actual Polar product IDs:
- Pro plan: `9861a6cf-914d-4693-84cd-946e5f5a44dc`
- Unlimited plan: `6fd5edc8-7215-4fea-815f-2ff8ddbe6e14`

## 🚀 Next Steps to Enable Real Polar Checkout

To complete the Polar integration, you need to add these to your `.env` file:

```bash
# Enable Polar
NEXT_PUBLIC_ENABLE_POLAR=true
NEXT_PUBLIC_POLAR_ENV=sandbox

# Required: Get these from https://polar.sh/dashboard/settings/api
POLAR_ACCESS_TOKEN_SANDBOX=polar_at_sandbox_YOUR_TOKEN_HERE
POLAR_BENEFIT_PRO_ID_SANDBOX=benefit_YOUR_BENEFIT_ID_HERE

# Optional but recommended
POLAR_WEBHOOK_SECRET_SANDBOX=whsec_YOUR_WEBHOOK_SECRET
POLAR_CREATE_CUSTOMER_ON_SIGNUP=true
POLAR_ENABLE_CUSTOMER_PORTAL=true
POLAR_ENABLE_CHECKOUT=true

# Make sure this matches your dev server
NEXT_PUBLIC_APP_URL=http://localhost:3001
```

## 📋 What You Still Need from Polar Dashboard

1. **API Token**: 
   - Go to https://polar.sh/dashboard/settings/api
   - Create a new token for sandbox environment
   - Copy the token (starts with `polar_at_sandbox_`)

2. **Benefit ID for Pro Plan**:
   - Go to your Pro product in Polar dashboard
   - Create or find a benefit for the Pro plan
   - Copy the benefit ID (starts with `benefit_`)

3. **Optional - Webhook Secret**:
   - Go to https://polar.sh/dashboard/settings/webhooks
   - Create a webhook pointing to your app
   - Copy the webhook secret (starts with `whsec_`)

## 🧪 Testing After Setup

Once you have the tokens:

1. **Update your `.env`** with the actual values
2. **Restart the dev server**: `npm run dev`
3. **Test checkout**: Click "Start free trial" - should redirect to real Polar checkout
4. **Test subscription**: Complete a test checkout and verify dashboard access

## 🔄 Quick Development Fix (Current)

If you want to continue testing without setting up Polar tokens yet:

```bash
NEXT_PUBLIC_ENABLE_POLAR=false
```

This will:
- Stop the `getBillingState` errors
- Allow the app to work with mock subscription data
- Let you test the subscription guard and user data features

## Current Payment Plans Configuration

The app now has your real Polar products configured:
- `pro-monthly`: $9/month Pro plan (ID: 9861a6cf-914d-4693-84cd-946e5f5a44dc)
- `unlimited-monthly`: $29/month Unlimited plan (ID: 6fd5edc8-7215-4fea-815f-2ff8ddbe6e14)

## Troubleshooting

- **Still getting errors?** Make sure to restart the dev server after changing `.env`
- **Checkout not working?** Verify your API token and benefit ID are correct
- **getBillingState failing?** Double-check your API token and app URL 