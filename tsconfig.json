{"compilerOptions": {"esModuleInterop": true, "skipLibCheck": true, "target": "es2022", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "strict": true, "noUncheckedIndexedAccess": true, "checkJs": false, "lib": ["dom", "dom.iterable", "ES2022"], "noEmit": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "plugins": [{"name": "next"}], "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "content-collections": ["./.content-collections/generated"]}}, "include": [".eslintrc.cjs", "next-serverEnv.d.ts", "**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.js", ".next/types/**/*.ts", ".content-collections/generated"], "exclude": ["node_modules"]}