# Polar Payment Integration Setup

## Overview
This app uses Polar.sh for subscription management and billing. The integration is optional and can be disabled.

## 🔧 Environment Configuration

### Required Environment Variables

To enable Polar integration, add these to your `.env` file:

```bash
# Enable Polar integration
NEXT_PUBLIC_ENABLE_POLAR=true

# Environment (sandbox or production)
NEXT_PUBLIC_POLAR_ENV=sandbox

# Polar access tokens
POLAR_ACCESS_TOKEN_SANDBOX=your_sandbox_token_here
POLAR_ACCESS_TOKEN_PROD=your_production_token_here

# Polar benefit IDs (for Pro plan detection)
POLAR_BENEFIT_PRO_ID_SANDBOX=your_sandbox_benefit_id
POLAR_BENEFIT_PRO_ID_PROD=your_production_benefit_id

# Optional: Webhook secrets
POLAR_WEBHOOK_SECRET_SANDBOX=your_sandbox_webhook_secret
POLAR_WEBHOOK_SECRET_PROD=your_production_webhook_secret

# Polar features (default to true)
POLAR_CREATE_CUSTOMER_ON_SIGNUP=true
POLAR_ENABLE_CUSTOMER_PORTAL=true
POLAR_ENABLE_CHECKOUT=true
```

## 🚫 Disabling Polar (Recommended for Development)

If you want to disable Polar integration entirely:

```bash
# Disable Polar integration
NEXT_PUBLIC_ENABLE_POLAR=false
```

When disabled:
- Billing tab will show "not available" message
- All users will be treated as free plan
- No API calls to Polar will be made
- No subscription checks

## 🛠️ Development Setup

### Option 1: Disable Polar (Easiest)
```bash
NEXT_PUBLIC_ENABLE_POLAR=false
```

### Option 2: Use Polar Sandbox
1. Create a Polar.sh account
2. Get sandbox credentials
3. Configure environment variables above
4. Set up products and benefits in Polar dashboard

## 🐛 Troubleshooting

### Error: "polar.getBillingState failed"

**Solution 1: Disable Polar**
```bash
NEXT_PUBLIC_ENABLE_POLAR=false
```

**Solution 2: Check Configuration**
- Verify all required environment variables are set
- Check Polar dashboard for correct API keys
- Ensure benefit IDs match your Polar setup

### Common Issues

1. **Missing Environment Variables**
   - Make sure `.env` file exists and has correct variables
   - Restart development server after adding variables

2. **Invalid API Keys**
   - Check Polar dashboard for correct tokens
   - Ensure you're using sandbox keys for development

3. **Network Issues**
   - Check internet connection
   - Verify Polar API is accessible

## 🔄 Development Workflow

For most route optimization development work, you can safely disable Polar:

```bash
# In your .env file
NEXT_PUBLIC_ENABLE_POLAR=false
```

This allows you to focus on:
- Route optimization features
- Google OAuth integration
- Core app functionality

Enable Polar only when working on:
- Subscription features
- Billing management
- Pro plan limitations

## 📚 Additional Resources

- [Polar.sh Documentation](https://docs.polar.sh/)
- [Better Auth Polar Plugin](https://www.better-auth.com/docs/plugins/polar)
- [Polar API Reference](https://docs.polar.sh/api) 